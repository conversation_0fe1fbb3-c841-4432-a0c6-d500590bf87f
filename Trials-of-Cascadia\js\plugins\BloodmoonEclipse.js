/*:
 * @target MZ
 * @plugindesc Bloodmoon Eclipse v1.0.0 - Atmospheric Eclipse Simulation
 *
 * @command StartBloodmoonEclipse
 * @text Start Bloodmoon Eclipse
 * @desc Start the bloodmoon eclipse atmospheric effect
 *
 * @command StopBloodmoonEclipse
 * @text Stop Bloodmoon Eclipse
 * @desc Stop the bloodmoon eclipse effect
 *
 * @param Duration
 * @text Eclipse Duration
 * @type number
 * @min 60
 * @max 1800
 * @default 600
 * @desc Duration of the eclipse in frames (10-30 seconds at 60fps)
 *
 * @param Intensity
 * @text Eclipse Intensity
 * @type number
 * @min 0.1
 * @max 1.0
 * @decimals 1
 * @default 0.8
 * @desc Intensity of the eclipse effect (0.1-1.0)
 *
 * @param BloodColor
 * @text Blood Moon Color
 * @type string
 * @default #8B0000
 * @desc Color of the blood moon during eclipse
 *
 * @param AtmosphericFog
 * @text Atmospheric Fog
 * @type boolean
 * @default true
 * @desc Enable atmospheric fog during eclipse
 *
 * @param ParticleEffects
 * @text Particle Effects
 * @type boolean
 * @default true
 * @desc Enable particle effects during eclipse
 *
 * @help BloodmoonEclipse.js
 *
 * A dramatic atmospheric plugin that simulates a bloodmoon eclipse
 * using canvas shapes and PIXI effects. Creates an immersive
 * celestial event with dynamic lighting, particle effects, and
 * atmospheric changes.
 *
 * FEATURES:
 * - Realistic moon eclipse progression
 * - Dynamic atmospheric lighting changes
 * - Particle effects (dust, ash, celestial particles)
 * - Blood moon coloration with realistic shading
 * - Atmospheric fog and haze effects
 * - Sound effects for immersion
 * - Customizable duration and intensity
 * - Smooth transitions and animations
 *
 * USAGE:
 * - Call "Start Bloodmoon Eclipse" from events
 * - Use "Stop Bloodmoon Eclipse" to end the effect
 * - Configure parameters in plugin settings
 *
 * The eclipse creates a dramatic atmosphere perfect for:
 * - Horror scenes
 * - Apocalyptic events
 * - Celestial rituals
 * - Atmospheric storytelling
 */

(() => {
    'use strict';

    const pluginName = 'BloodmoonEclipse';
    const parameters = PluginManager.parameters(pluginName);

    // Plugin parameters
    const DURATION = Number(parameters['Duration'] || 600);
    const INTENSITY = Number(parameters['Intensity'] || 0.8);
    const BLOOD_COLOR = parameters['BloodColor'] || '#8B0000';
    const ATMOSPHERIC_FOG = parameters['AtmosphericFog'] !== 'false';
    const PARTICLE_EFFECTS = parameters['ParticleEffects'] !== 'false';

    // Eclipse state
    let eclipseActive = false;
    let eclipseTimer = 0;
    let eclipsePhase = 0; // 0-1: start to peak, 1-2: peak to end
    let eclipseProgress = 0;
    let originalTint = null;
    let originalBrightness = null;

    // Particle systems
    let atmosphericParticles = [];
    let celestialParticles = [];
    let dustParticles = [];

    // PIXI containers for effects
    let eclipseContainer = null;
    let particleContainer = null;
    let fogContainer = null;

    // Audio context for sound effects
    let audioContext = null;
    let windOscillator = null;
    let ambientOscillator = null;

    // Plugin commands
    PluginManager.registerCommand(pluginName, 'StartBloodmoonEclipse', args => {
        startEclipse();
    });

    PluginManager.registerCommand(pluginName, 'StopBloodmoonEclipse', args => {
        stopEclipse();
    });

    // Main eclipse class
    class BloodmoonEclipse {
        constructor() {
            this.active = false;
            this.timer = 0;
            this.phase = 0;
            this.progress = 0;
            this.intensity = INTENSITY;
            this.duration = DURATION;
            
            // Visual elements
            this.sunSprite = null;
            this.moonSprite = null;
            this.corona = null;
            this.atmosphericGlow = null;
            this.bloodOverlay = null;
            
            // Animation properties
            this.animationTimer = 0;
            this.solarFlares = [];
            this.sunspots = [];
            
            // Enhanced eclipse features
            this.baileyBeads = [];
            this.diamondRing = null;
            this.solarProminences = [];
            this.cameraZoom = 1.0;
            this.cameraShake = 0;
            this.lensFlare = null;
            
            // Particle systems
            this.atmosphericParticles = [];
            this.celestialParticles = [];
            this.dustParticles = [];
            
            // Audio
            this.audioContext = null;
            this.windOscillator = null;
            this.ambientOscillator = null;
            this.windGain = null;
            this.ambientGain = null;
            
            // Vein path system
            this.veinPaths = []; // Store vein path data
            this.pathsGenerated = false; // Track if paths have been generated
            
            // Memory management
            this.pendingTimeouts = new Set();
            this.activeGraphics = new Set();
            
            // Performance optimizations
            this.sunTextureCache = null;
            this.lastSunUpdate = 0;
            this.sunUpdateInterval = 10; // Update sun texture every 10 frames instead of every frame
            this.fogGraphics = null;
            this.lastFogIntensity = 0;
            
            this.initialize();
        }

        initialize() {
            // Create PIXI containers
            this.createContainers();
            
            // Initialize audio
            this.initializeAudio();
            
            // Create visual elements
            this.createVisualElements();
            
            // Create particle systems
            if (PARTICLE_EFFECTS) {
                this.createParticleSystems();
            }
        }

        createContainers() {
            // Main eclipse container
            this.container = new PIXI.Container();
            this.container.zIndex = 1000;
            
            // Particle container
            this.particleContainer = new PIXI.Container();
            this.particleContainer.zIndex = 1001;
            
            // Fog container
            this.fogContainer = new PIXI.Container();
            this.fogContainer.zIndex = 999;
        }

                 initializeAudio() {
             // Audio removed for silent eclipse
             this.audioContext = null;
             this.windOscillator = null;
             this.ambientOscillator = null;
             this.windGain = null;
             this.ambientGain = null;
         }

        createVisualElements() {
            // Create sun sprite (center of screen)
            this.createSun();
            
            // Create corona effect (visible during total eclipse)
            this.createCorona();
            
            // Create moon sprite (starts off-screen, moves across sun) - on top of corona
            this.createMoon();
            
            // Create atmospheric glow
            this.createAtmosphericGlow();
            
            // Create blood overlay
            this.createBloodOverlay();
        }

        createSun() {
            const sunSize = 150;
            const canvasSize = 800; // Doubled canvas size to prevent square boundaries
            const sunGraphics = new PIXI.Graphics();
            
            // Create sun using a larger canvas to avoid square boundaries
            const sunTexture = this.createSunTexture(sunSize, canvasSize);
            const sunSprite = new PIXI.Sprite(sunTexture);
            sunSprite.anchor.set(0.5);
            
            this.sunSprite = sunSprite;
            this.sunSprite.x = Graphics.width / 2;
            this.sunSprite.y = Graphics.height / 2;
            this.container.addChild(this.sunSprite);
        }

        createSunTexture(sunSize, canvasSize) {
            const canvas = document.createElement('canvas');
            canvas.width = canvasSize;
            canvas.height = canvasSize;
            const ctx = canvas.getContext('2d');
            
            const centerX = canvasSize / 2;
            const centerY = canvasSize / 2;
            
            // Create more realistic sun with multiple layers
            const coreGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, sunSize * 0.3);
            coreGradient.addColorStop(0, '#FFFFFF');
            coreGradient.addColorStop(0.5, '#FFFFE0');
            coreGradient.addColorStop(1, '#FFFF00');
            
            const photosphereGradient = ctx.createRadialGradient(centerX, centerY, sunSize * 0.2, centerX, centerY, sunSize);
            photosphereGradient.addColorStop(0, '#FFFF00');
            photosphereGradient.addColorStop(0.6, '#FFD700');
            photosphereGradient.addColorStop(1, '#FFA500');
            
            // Draw photosphere (main sun surface)
            ctx.fillStyle = photosphereGradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, sunSize, 0, Math.PI * 2);
            ctx.fill();
            
            // Draw core
            ctx.fillStyle = coreGradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, sunSize * 0.3, 0, Math.PI * 2);
            ctx.fill();
            
            // Add realistic solar granules (surface texture)
            ctx.fillStyle = 'rgba(255, 255, 0, 0.3)';
            for (let i = 0; i < 50; i++) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * sunSize * 0.8;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                const granuleSize = 2 + Math.random() * 8;
                
                ctx.beginPath();
                ctx.arc(x, y, granuleSize, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // Store solar flare data for animation
            this.solarFlares = [];
            for (let i = 0; i < 12; i++) {
                const angle = Math.random() * Math.PI * 2;
                const radius = sunSize * 0.9 + Math.random() * sunSize * 0.2;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                const flareSize = 3 + Math.random() * 8;
                
                this.solarFlares.push({
                    x: x,
                    y: y,
                    baseSize: flareSize,
                    phase: Math.random() * Math.PI * 2,
                    speed: 0.02 + Math.random() * 0.03
                });
            }
            
            // Store sunspot data for animation
            this.sunspots = [];
            for (let i = 0; i < 6; i++) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * sunSize * 0.7;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                const spotSize = 2 + Math.random() * 6;
                
                this.sunspots.push({
                    x: x,
                    y: y,
                    baseSize: spotSize,
                    phase: Math.random() * Math.PI * 2,
                    speed: 0.01 + Math.random() * 0.02
                });
            }
            
            // Draw initial solar flares
            for (let flare of this.solarFlares) {
                // Create realistic solar flares
                ctx.fillStyle = 'rgba(255, 200, 0, 0.4)';
                ctx.beginPath();
                ctx.arc(flare.x, flare.y, flare.baseSize, 0, Math.PI * 2);
                ctx.fill();
                
                // Add flare glow
                ctx.fillStyle = 'rgba(255, 150, 0, 0.2)';
                ctx.beginPath();
                ctx.arc(flare.x, flare.y, flare.baseSize * 1.5, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // Draw initial sunspots
            for (let spot of this.sunspots) {
                // Sunspot (darker area)
                ctx.fillStyle = 'rgba(200, 100, 0, 0.3)';
                ctx.beginPath();
                ctx.arc(spot.x, spot.y, spot.baseSize, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // Add outer corona glow (more natural)
            const coronaGradient = ctx.createRadialGradient(centerX, centerY, sunSize, centerX, centerY, sunSize * 2);
            coronaGradient.addColorStop(0, 'rgba(255, 165, 0, 0.2)');
            coronaGradient.addColorStop(0.5, 'rgba(255, 165, 0, 0.1)');
            coronaGradient.addColorStop(1, 'rgba(255, 165, 0, 0)');
            
            ctx.fillStyle = coronaGradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, sunSize * 2, 0, Math.PI * 2);
            ctx.fill();
            
            return PIXI.Texture.from(canvas);
        }

        createMoon() {
            const moonSize = 140; // Slightly larger than sun to create total eclipse
            const canvasSize = 700; // Doubled canvas size to prevent square boundaries
            const moonTexture = this.createMoonTexture(moonSize, canvasSize);
            const moonSprite = new PIXI.Sprite(moonTexture);
            moonSprite.anchor.set(0.5);
            
            this.moonSprite = moonSprite;
            // Start moon off-screen to the left
            this.moonSprite.x = -moonSize;
            this.moonSprite.y = Graphics.height / 2;
            this.moonSprite.zIndex = 1001; // Higher than veins to stay on top
            this.container.addChild(this.moonSprite);
        }

        createMoonTexture(moonSize, canvasSize) {
            const canvas = document.createElement('canvas');
            canvas.width = canvasSize;
            canvas.height = canvasSize;
            const ctx = canvas.getContext('2d');
            
            const centerX = canvasSize / 2;
            const centerY = canvasSize / 2;
            
            // Create more realistic moon with multiple layers - much lighter
            const moonGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, moonSize);
            moonGradient.addColorStop(0, '#888888');
            moonGradient.addColorStop(0.7, '#777777');
            moonGradient.addColorStop(1, '#666666');
            
            // Draw moon base
            ctx.fillStyle = moonGradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, moonSize, 0, Math.PI * 2);
            ctx.fill();
            
            // Add realistic lunar maria (dark regions) - just one shade darker
            ctx.fillStyle = '#777777';
            for (let i = 0; i < 8; i++) {
                const angle = Math.random() * Math.PI * 2;
                const radius = 10 + Math.random() * moonSize * 0.6;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                const mariaSize = 15 + Math.random() * 25;
                
                ctx.beginPath();
                ctx.arc(x, y, mariaSize, 0, Math.PI * 2);
                ctx.fill();
            }
            
                         // Add lunar craters with realistic shadows (evenly distributed)
             const craterCount = 12;
             const sectors = 4; // Divide moon into 4 quadrants
             const cratersPerSector = Math.ceil(craterCount / sectors);
             
             for (let sector = 0; sector < sectors; sector++) {
                 for (let i = 0; i < cratersPerSector; i++) {
                     // Calculate position within each sector
                     const sectorAngle = (Math.PI / 2) * sector; // 0, 90, 180, 270 degrees
                     const angleOffset = (Math.PI / 2) * (i / cratersPerSector); // Distribute within sector
                     const angle = sectorAngle + angleOffset + (Math.random() - 0.5) * 0.3; // Add some randomness
                     
                     // Vary radius to avoid clustering at edges
                     const minRadius = moonSize * 0.2;
                     const maxRadius = moonSize * 0.7;
                     const radius = minRadius + Math.random() * (maxRadius - minRadius);
                     
                     const x = centerX + Math.cos(angle) * radius;
                     const y = centerY + Math.sin(angle) * radius;
                     const craterSize = 1 + Math.random() * 6;
                     
                     // Crater shadow (much more subtle)
                     ctx.fillStyle = '#777777';
                     ctx.beginPath();
                     ctx.arc(x + 1, y + 1, craterSize, 0, Math.PI * 2);
                     ctx.fill();
                     
                     // Crater base
                     ctx.fillStyle = '#888888';
                     ctx.beginPath();
                     ctx.arc(x, y, craterSize, 0, Math.PI * 2);
                     ctx.fill();
                     
                     // Crater rim (more subtle)
                     ctx.fillStyle = '#999999';
                     ctx.beginPath();
                     ctx.arc(x, y, craterSize * 0.7, 0, Math.PI * 2);
                     ctx.fill();
                 }
             }
            
            // Add subtle surface texture
            ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
            for (let i = 0; i < 100; i++) {
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * moonSize * 0.9;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                const textureSize = 1 + Math.random() * 3;
                
                ctx.beginPath();
                ctx.arc(x, y, textureSize, 0, Math.PI * 2);
                ctx.fill();
            }
            
            return PIXI.Texture.from(canvas);
        }

        createCorona() {
            const coronaSize = 200;
            const coronaGraphics = new PIXI.Graphics();
            
            // Create corona texture
            const coronaTexture = this.createCoronaTexture(coronaSize);
            const coronaSprite = new PIXI.Sprite(coronaTexture);
            coronaSprite.anchor.set(0.5);
            
            this.corona = coronaSprite;
            this.corona.x = Graphics.width / 2;
            this.corona.y = Graphics.height / 2;
            this.corona.alpha = 0;
            this.container.addChild(this.corona);
        }

        createCoronaTexture(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size * 4;
            canvas.height = size * 4;
            const ctx = canvas.getContext('2d');
            
            const centerX = size * 2;
            const centerY = size * 2;
            
            // Create much more powerful and dramatic corona with multiple layers
            const innerCorona = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, size * 0.3);
            innerCorona.addColorStop(0, 'rgba(255, 255, 255, 1.0)');
            innerCorona.addColorStop(0.2, 'rgba(255, 255, 255, 0.95)');
            innerCorona.addColorStop(0.5, 'rgba(255, 255, 255, 0.8)');
            innerCorona.addColorStop(0.8, 'rgba(255, 255, 255, 0.5)');
            innerCorona.addColorStop(1, 'rgba(255, 255, 255, 0)');
            
            const middleCorona = ctx.createRadialGradient(centerX, centerY, size * 0.1, centerX, centerY, size * 0.8);
            middleCorona.addColorStop(0, 'rgba(255, 255, 0, 1.0)');
            middleCorona.addColorStop(0.3, 'rgba(255, 255, 0, 0.9)');
            middleCorona.addColorStop(0.6, 'rgba(255, 200, 0, 0.7)');
            middleCorona.addColorStop(0.9, 'rgba(255, 150, 0, 0.4)');
            middleCorona.addColorStop(1, 'rgba(255, 150, 0, 0)');
            
            const outerCorona = ctx.createRadialGradient(centerX, centerY, size * 0.3, centerX, centerY, size * 1.5);
            outerCorona.addColorStop(0, 'rgba(255, 150, 0, 0.8)');
            outerCorona.addColorStop(0.4, 'rgba(255, 100, 0, 0.6)');
            outerCorona.addColorStop(0.7, 'rgba(255, 50, 0, 0.3)');
            outerCorona.addColorStop(0.9, 'rgba(255, 25, 0, 0.1)');
            outerCorona.addColorStop(1, 'rgba(255, 25, 0, 0)');
            
            // Draw corona layers
            ctx.fillStyle = innerCorona;
            ctx.fillRect(0, 0, size * 4, size * 4);
            
            ctx.fillStyle = middleCorona;
            ctx.fillRect(0, 0, size * 4, size * 4);
            
            ctx.fillStyle = outerCorona;
            ctx.fillRect(0, 0, size * 4, size * 4);
            
            // Add much more dramatic corona streamers (main spikes)
            for (let i = 0; i < 16; i++) {
                const angle = (Math.PI * 2 * i) / 16;
                const streamerLength = size * (1.0 + Math.random() * 0.6);
                const startX = centerX + Math.cos(angle) * (size * 0.5);
                const startY = centerY + Math.sin(angle) * (size * 0.5);
                const endX = centerX + Math.cos(angle) * streamerLength;
                const endY = centerY + Math.sin(angle) * streamerLength;
                
                // Create gradient for streamer
                const streamerGradient = ctx.createLinearGradient(startX, startY, endX, endY);
                streamerGradient.addColorStop(0, 'rgba(255, 255, 255, 1.0)');
                streamerGradient.addColorStop(0.2, 'rgba(255, 255, 255, 0.95)');
                streamerGradient.addColorStop(0.4, 'rgba(255, 255, 0, 0.9)');
                streamerGradient.addColorStop(0.7, 'rgba(255, 200, 0, 0.6)');
                streamerGradient.addColorStop(1, 'rgba(255, 150, 0, 0.2)');
                
                ctx.strokeStyle = streamerGradient;
                ctx.lineWidth = 6 + Math.random() * 4;
                ctx.lineCap = 'round';
                
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }
            
            // Add secondary corona rays
            for (let i = 0; i < 16; i++) {
                const angle = (Math.PI * 2 * i) / 16 + Math.PI / 16;
                const rayLength = size * (0.9 + Math.random() * 0.3);
                const startX = centerX + Math.cos(angle) * (size * 0.7);
                const startY = centerY + Math.sin(angle) * (size * 0.7);
                const endX = centerX + Math.cos(angle) * rayLength;
                const endY = centerY + Math.sin(angle) * rayLength;
                
                const rayGradient = ctx.createLinearGradient(startX, startY, endX, endY);
                rayGradient.addColorStop(0, 'rgba(255, 255, 255, 0.6)');
                rayGradient.addColorStop(0.5, 'rgba(255, 200, 0, 0.4)');
                rayGradient.addColorStop(1, 'rgba(255, 150, 0, 0.1)');
                
                ctx.strokeStyle = rayGradient;
                ctx.lineWidth = 2 + Math.random() * 2;
                ctx.lineCap = 'round';
                
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }
            
            // Add subtle corona loops
            for (let i = 0; i < 6; i++) {
                const angle = (Math.PI * 2 * i) / 6 + Math.PI / 6;
                const loopRadius = size * (0.5 + Math.random() * 0.3);
                const loopX = centerX + Math.cos(angle) * loopRadius;
                const loopY = centerY + Math.sin(angle) * loopRadius;
                const loopSize = 10 + Math.random() * 20;
                
                ctx.strokeStyle = 'rgba(255, 200, 0, 0.3)';
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                
                ctx.beginPath();
                ctx.arc(loopX, loopY, loopSize, 0, Math.PI * 2);
                ctx.stroke();
            }
            
            return PIXI.Texture.from(canvas);
        }



        createAtmosphericGlow() {
            const glowGraphics = new PIXI.Graphics();
            
            // Create atmospheric glow effect
            const glowTexture = this.createAtmosphericTexture();
            const glowSprite = new PIXI.Sprite(glowTexture);
            glowSprite.anchor.set(0.5);
            glowSprite.x = Graphics.width / 2;
            glowSprite.y = Graphics.height / 2;
            glowSprite.alpha = 0;
            
            this.atmosphericGlow = glowSprite;
            this.container.addChild(this.atmosphericGlow);
            

            
            // Create heat distortion effects (atmospheric phenomena)
            this.createHeatDistortion();
        }
        

        
        createHeatDistortion() {
            // Create heat distortion container
            this.heatDistortionContainer = new PIXI.Container();
            this.container.addChild(this.heatDistortionContainer);
            
            // Create distortion waves
            for (let i = 0; i < 5; i++) {
                const distortionGraphics = new PIXI.Graphics();
                distortionGraphics.beginFill(0xFFFFFF, 0.1);
                distortionGraphics.drawRect(0, 0, Graphics.width, 2);
                distortionGraphics.endFill();
                distortionGraphics.y = Graphics.height * 0.2 + i * 30;
                distortionGraphics.alpha = 0;
                
                this.heatDistortionContainer.addChild(distortionGraphics);
            }
        }

        createAtmosphericTexture() {
            const canvas = document.createElement('canvas');
            canvas.width = Graphics.width;
            canvas.height = Graphics.height;
            const ctx = canvas.getContext('2d');
            
            // Create more natural atmospheric scattering effect
            const centerX = Graphics.width / 2;
            const centerY = Graphics.height / 2;
            const maxRadius = Math.max(Graphics.width, Graphics.height) / 2;
            
            // Primary atmospheric glow (more natural, less red)
            const primaryGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, maxRadius);
            primaryGradient.addColorStop(0, 'rgba(255, 200, 0, 0.1)');
            primaryGradient.addColorStop(0.3, 'rgba(255, 200, 0, 0.05)');
            primaryGradient.addColorStop(0.7, 'rgba(255, 200, 0, 0.02)');
            primaryGradient.addColorStop(1, 'rgba(255, 200, 0, 0)');
            
            ctx.fillStyle = primaryGradient;
            ctx.fillRect(0, 0, Graphics.width, Graphics.height);
            
            // Add subtle atmospheric layers
            const secondaryGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, maxRadius * 0.8);
            secondaryGradient.addColorStop(0, 'rgba(255, 150, 0, 0.03)');
            secondaryGradient.addColorStop(0.5, 'rgba(255, 150, 0, 0.01)');
            secondaryGradient.addColorStop(1, 'rgba(255, 150, 0, 0)');
            
            ctx.fillStyle = secondaryGradient;
            ctx.fillRect(0, 0, Graphics.width, Graphics.height);
            
            // Add atmospheric haze
            const hazeGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, maxRadius * 0.6);
            hazeGradient.addColorStop(0, 'rgba(255, 255, 255, 0.02)');
            hazeGradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.01)');
            hazeGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            
            ctx.fillStyle = hazeGradient;
            ctx.fillRect(0, 0, Graphics.width, Graphics.height);
            
            return PIXI.Texture.from(canvas);
        }

        createBloodOverlay() {
            // Removed red blood overlay for more natural look
            this.bloodOverlay = null;
        }
        
        generateVeinPaths() {
            if (this.pathsGenerated) return; // Only generate once
            
            this.veinPaths = [];
            const veinCount = 8; // Number of main veins
            
            for (let i = 0; i < veinCount; i++) {
                const veinPath = {
                    mainAngle: (Math.PI * 2 * i) / veinCount,
                    branchCount: 2 + Math.floor(Math.random() * 2), // 2-3 branches
                    branches: []
                };
                
                // Generate random branches for this vein
                for (let j = 0; j < veinPath.branchCount; j++) {
                    const branch = {
                        angleOffset: (Math.random() - 0.5) * Math.PI / 2, // Random angle offset
                        subBranchCount: 1 + Math.floor(Math.random() * 2), // 1-2 sub-branches
                        subBranches: []
                    };
                    
                    // Generate random sub-branches
                    for (let k = 0; k < branch.subBranchCount; k++) {
                        const subBranch = {
                            angleOffset: (Math.random() - 0.5) * Math.PI / 3 // Random sub-branch angle
                        };
                        branch.subBranches.push(subBranch);
                    }
                    
                    veinPath.branches.push(branch);
                }
                
                this.veinPaths.push(veinPath);
            }
            
            this.pathsGenerated = true;
        }
        
        // Safe timeout wrapper that tracks and cleans up timeouts
        safeSetTimeout(callback, delay) {
            const timeoutId = setTimeout(() => {
                this.pendingTimeouts.delete(timeoutId);
                if (this.active) {
                    callback();
                }
            }, delay);
            this.pendingTimeouts.add(timeoutId);
            return timeoutId;
        }
        
        // Clear all pending timeouts
        clearAllTimeouts() {
            for (const timeoutId of this.pendingTimeouts) {
                clearTimeout(timeoutId);
            }
            this.pendingTimeouts.clear();
        }
        
        // Clean up graphics objects
        cleanupGraphics() {
            for (const graphics of this.activeGraphics) {
                if (graphics && graphics.parent) {
                    graphics.parent.removeChild(graphics);
                }
                if (graphics && graphics.destroy) {
                    graphics.destroy();
                }
            }
            this.activeGraphics.clear();
        }

                 createParticleSystems() {
             // Particles removed for clean visual experience
         }

        createAtmosphericParticles() {
            for (let i = 0; i < 80; i++) {
                const particle = this.createParticle(0xFFD700, 0xFFFFE0);
                particle.x = Math.random() * Graphics.width;
                particle.y = Math.random() * Graphics.height;
                particle.vx = (Math.random() - 0.5) * 0.3;
                particle.vy = (Math.random() - 0.5) * 0.3;
                particle.life = 400 + Math.random() * 400;
                particle.maxLife = particle.life;
                particle.size = 1 + Math.random() * 3;
                particle.alpha = 0.3 + Math.random() * 0.4;
                
                this.atmosphericParticles.push(particle);
                this.particleContainer.addChild(particle);
            }
        }

        createCelestialParticles() {
            for (let i = 0; i < 50; i++) {
                const particle = this.createParticle(0xFFD700, 0xFFFFE0);
                particle.x = Math.random() * Graphics.width;
                particle.y = Math.random() * Graphics.height;
                particle.vx = (Math.random() - 0.5) * 0.8;
                particle.vy = (Math.random() - 0.5) * 0.8;
                particle.life = 300 + Math.random() * 300;
                particle.maxLife = particle.life;
                particle.size = 1 + Math.random() * 2;
                particle.alpha = 0.4 + Math.random() * 0.5;
                
                this.celestialParticles.push(particle);
                this.particleContainer.addChild(particle);
            }
        }

        createDustParticles() {
            for (let i = 0; i < 100; i++) {
                const particle = this.createParticle(0x696969, 0x808080);
                particle.x = Math.random() * Graphics.width;
                particle.y = Math.random() * Graphics.height;
                particle.vx = (Math.random() - 0.5) * 0.3;
                particle.vy = (Math.random() - 0.5) * 0.3;
                particle.life = 400 + Math.random() * 400;
                particle.maxLife = particle.life;
                particle.size = 1 + Math.random() * 2;
                
                this.dustParticles.push(particle);
                this.particleContainer.addChild(particle);
            }
        }

        createParticle(color, glowColor) {
            const graphics = new PIXI.Graphics();
            
            // Particle glow
            graphics.beginFill(glowColor);
            graphics.drawCircle(0, 0, 4);
            graphics.endFill();
            
            // Particle core
            graphics.beginFill(color);
            graphics.drawCircle(0, 0, 2);
            graphics.endFill();
            
            return graphics;
        }

                 start() {
             this.active = true;
             this.timer = 0;
             this.phase = 0;
             this.progress = 0;
            
            // Generate random vein paths once at start
            this.generateVeinPaths();
             
             // Store original scene properties
             this.storeOriginalProperties();
             
             // Show containers
             this.container.visible = true;
             this.fogContainer.visible = true;
         }

                 stop() {
             if (!this.active) return;
             
             this.active = false;
             
             try {
                 // Don't remove graphics immediately - let them fade with the scene
                 this.clearAllTimeouts();
                 
                 // Clean up particle arrays
                 this.atmosphericParticles = [];
                 this.celestialParticles = [];
                 this.dustParticles = [];
                 
                 // Clear other arrays
                 this.solarFlares = [];
                 this.sunspots = [];
                 this.baileyBeads = [];
                 this.solarProminences = [];
                 
                 // Reset animation timers
                 this.animationTimer = 0;
                 
                 // Restore original properties
                 this.restoreOriginalProperties();
                 
                 // Keep containers visible until scene transition completes
                 // They will be cleaned up when the scene is destroyed
                 
             } catch (e) {
                 console.log('Error stopping eclipse:', e);
             }
         }

        storeOriginalProperties() {
            if (this.scene && this.scene.background) {
                this.originalTint = this.scene.background.tint;
                this.originalBrightness = this.scene.background.brightness;
            }
        }

        restoreOriginalProperties() {
            if (this.scene && this.scene.background && this.originalTint !== null) {
                this.scene.background.tint = this.originalTint;
                this.scene.background.brightness = this.originalBrightness;
            }
        }

                 startAudio() {
             // Audio removed for silent eclipse
         }

         stopAudio() {
             // Audio removed for silent eclipse
         }

                 update() {
             if (!this.active) return;
             
             this.timer++;
             this.animationTimer++;
             this.updateEclipseProgress();
             this.updateVisualEffects();
             this.updateSunAnimation();
             this.updateAtmosphericEffects();
             
             // Check if eclipse is complete
             if (this.timer >= this.duration) {
                 this.stop();
             }
         }

        updateEclipseProgress() {
            // Calculate eclipse progress (0 to 1) - single pass
            this.progress = this.timer / this.duration;
            this.phase = 0;
        }

        updateVisualEffects() {
            // Update moon position (moves in an arc across screen, synced with other effects)
            if (this.moonSprite) {
                const screenWidth = Graphics.width;
                const screenHeight = Graphics.height;
                const moonSize = 140;
                
                // Define arc parameters
                const startX = -moonSize;
                const endX = screenWidth + moonSize;
                const arcHeight = screenHeight * 0.3; // How high the arc goes
                const centerY = screenHeight / 2;
                
                // Use linear progress to sync with sky, sun, and corona effects
                const progress = this.progress; // 0 to 1
                
                // Calculate arc movement
                const moonX = startX + (endX - startX) * progress;
                
                // Create arc using sine wave (smooth curve)
                // Start lower so moon crosses directly over the sun
                const arcOffset = Math.sin(progress * Math.PI) * arcHeight;
                const moonY = centerY + arcHeight - arcOffset;
                
                this.moonSprite.x = moonX;
                this.moonSprite.y = moonY;
            }
            
            // Sun stays bright - it gets covered by the moon, not dimmed
            if (this.sunSprite) {
                this.sunSprite.alpha = 1; // Sun always stays at full brightness
                
                // Change sun color after moon passes (blood moon effect)
                if (this.progress > 0.5) {
                    const colorProgress = (this.progress - 0.5) * 2; // 0 to 1
                    
                    // Transition from normal yellow to blood red
                    const redTint = Math.floor(255 * (0.8 + colorProgress * 0.2)); // 204 -> 255
                    const greenTint = Math.floor(255 * (0.6 - colorProgress * 0.4)); // 153 -> 51
                    const blueTint = Math.floor(255 * (0.2 - colorProgress * 0.2)); // 51 -> 0
                    
                    this.sunSprite.tint = (redTint << 16) | (greenTint << 8) | blueTint;
                    
                    // Add blood moon texture effects
                    this.updateBloodMoonTexture(colorProgress);
                } else {
                    // Normal sun color
                    this.sunSprite.tint = 0xFFFFFF;
                }
            }
            
            // Moon gets darker when in front of the sun (silhouette effect)
            if (this.moonSprite) {
                // Calculate how much the moon overlaps with the sun
                const sunCenterX = Graphics.width / 2;
                const sunCenterY = Graphics.height / 2;
                const sunRadius = 150;
                const moonRadius = 140;
                
                // Distance between moon and sun centers
                const distanceX = Math.abs(this.moonSprite.x - sunCenterX);
                const distanceY = Math.abs(this.moonSprite.y - sunCenterY);
                const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);
                
                // If moon is close to sun, make it darker (silhouette effect)
                if (distance < (sunRadius + moonRadius) * 0.8) {
                    const overlap = 1 - (distance / ((sunRadius + moonRadius) * 0.8));
                    const darkness = Math.min(1, overlap); // Go completely black
                    
                    // Apply dark tint instead of transparency
                    const tintValue = Math.floor(255 * (1 - darkness));
                    this.moonSprite.tint = (tintValue << 16) | (tintValue << 8) | tintValue;
                } else {
                    // Reset to normal brightness
                    this.moonSprite.tint = 0xFFFFFF;
                }
            }
            
            // Update corona (visible during total eclipse) - much more powerful
            if (this.corona) {
                // Corona appears gradually as eclipse approaches totality
                let coronaAlpha = 0;
                if (this.progress > 0.4 && this.progress < 0.6) {
                    // Smooth transition in and out, centered on 50% (moon crossing sun)
                    const inPhase = Math.max(0, (this.progress - 0.4) / 0.1);
                    const outPhase = Math.max(0, (0.6 - this.progress) / 0.1);
                    const baseAlpha = Math.min(inPhase, outPhase);
                    
                    // Make corona much more powerful and dramatic
                    coronaAlpha = baseAlpha * this.intensity * 2.5; // Increased intensity
                    
                    // Add pulsing effect for more dramatic appearance
                    const pulse = Math.sin(this.animationTimer * 0.1) * 0.3 + 0.7;
                    coronaAlpha *= pulse;
                }
                this.corona.alpha = Math.max(0, Math.min(1, coronaAlpha));
            }
            
            // Update atmospheric glow
            if (this.atmosphericGlow) {
                this.atmosphericGlow.alpha = Math.max(0, Math.min(1, this.progress * this.intensity * 0.3));
            }
            

            
            // Update heat distortion effects (atmospheric phenomena)
            if (this.heatDistortionContainer) {
                const distortionIntensity = Math.max(0, Math.min(1, this.progress * this.intensity * 0.3));
                this.heatDistortionContainer.children.forEach((distortion, index) => {
                    const wave = Math.sin(this.animationTimer * 0.03 + index * 0.4) * 0.5 + 0.5;
                    distortion.alpha = distortionIntensity * wave * 0.4;
                    distortion.x = Math.sin(this.animationTimer * 0.015 + index * 0.2) * 10;
                });
            }
            
            // Update Baily's Beads effect (bright spots around moon during totality)
            this.updateBaileysBeads();
            
            // Update Diamond Ring effect
            this.updateDiamondRing();
            
            // Update camera effects
            this.updateCameraEffects();
            
            // Update blood overlay (removed for natural look)
            // if (this.bloodOverlay) {
            //     this.bloodOverlay.alpha = Math.max(0, Math.min(1, this.progress * this.intensity * 0.2));
            // }
            
            // Update scene tint with enhanced color grading (safely) - gets darker during eclipse
            if (this.scene && this.scene.background) {
                try {
                    const tintIntensity = Math.max(0, Math.min(1, this.progress * this.intensity));
                    
                    // Enhanced color grading with chromatic aberration
                    const redTint = Math.floor(255 * (1 - tintIntensity * 0.3));
                    const greenTint = Math.floor(255 * (1 - tintIntensity * 0.5));
                    const blueTint = Math.floor(255 * (1 - tintIntensity * 0.7));
                    
                    // Add subtle color shifts for atmospheric effect
                    const colorShift = Math.sin(this.animationTimer * 0.01) * 0.1;
                    const adjustedRed = Math.max(0, Math.min(255, redTint + colorShift * 20));
                    const adjustedGreen = Math.max(0, Math.min(255, greenTint + colorShift * 15));
                    const adjustedBlue = Math.max(0, Math.min(255, blueTint + colorShift * 10));
                    
                    this.scene.background.tint = (adjustedRed << 16) | (adjustedGreen << 8) | adjustedBlue;
                    
                    // Add subtle brightness variation for more dramatic effect
                    const brightnessVariation = Math.sin(this.animationTimer * 0.005) * 0.05;
                    this.scene.background.alpha = Math.max(0.8, Math.min(1, 1 - tintIntensity * 0.3 + brightnessVariation));
                    
                } catch (e) {
                    console.log('Error updating scene tint:', e);
                }
            }
        }

                 updateParticles() {
             // Particles removed for clean visual experience
         }

        updateParticleArray(particles) {
            for (let i = particles.length - 1; i >= 0; i--) {
                const particle = particles[i];
                
                try {
                    // Update position
                    particle.x += particle.vx || 0;
                    particle.y += particle.vy || 0;
                    
                    // Update life
                    particle.life--;
                    
                    // Update alpha based on life (safely)
                    if (particle.maxLife && particle.maxLife > 0) {
                        particle.alpha = Math.max(0, Math.min(1, particle.life / particle.maxLife));
                    }
                    
                    // Remove dead particles
                    if (particle.life <= 0) {
                        if (this.particleContainer && this.particleContainer.removeChild) {
                            this.particleContainer.removeChild(particle);
                        }
                        particles.splice(i, 1);
                        
                        // Create new particle
                        this.createNewParticle(particles, particle);
                    }
                    
                    // Wrap around screen
                    if (particle.x < -10) particle.x = Graphics.width + 10;
                    if (particle.x > Graphics.width + 10) particle.x = -10;
                    if (particle.y < -10) particle.y = Graphics.height + 10;
                    if (particle.y > Graphics.height + 10) particle.y = -10;
                } catch (e) {
                    console.log('Error updating particle:', e);
                    // Remove problematic particle
                    if (this.particleContainer && this.particleContainer.removeChild) {
                        this.particleContainer.removeChild(particle);
                    }
                    particles.splice(i, 1);
                }
            }
        }

        createNewParticle(particleArray, oldParticle) {
            const particle = this.createParticle(oldParticle.tint, oldParticle.tint);
            particle.x = Math.random() * Graphics.width;
            particle.y = Math.random() * Graphics.height;
            particle.vx = oldParticle.vx;
            particle.vy = oldParticle.vy;
            particle.life = oldParticle.maxLife;
            particle.maxLife = oldParticle.maxLife;
            particle.size = oldParticle.size;
            
            particleArray.push(particle);
            this.particleContainer.addChild(particle);
        }

        updateAtmosphericEffects() {
            if (ATMOSPHERIC_FOG) {
                this.updateFogEffect();
            }
        }

        updateFogEffect() {
            const fogIntensity = this.progress * this.intensity * 0.3;
            
            // Performance optimization: only update fog when intensity changes significantly
            if (!this.fogGraphics) {
                this.fogGraphics = new PIXI.Graphics();
                this.fogContainer.addChild(this.fogGraphics);
            }
            
            // Clear and redraw fog only when needed
            if (Math.abs(this.lastFogIntensity - fogIntensity) > 0.01) {
                this.fogGraphics.clear();
            
            if (fogIntensity > 0) {
                    this.fogGraphics.beginFill(0x000000, fogIntensity);
                    this.fogGraphics.drawRect(0, 0, Graphics.width, Graphics.height);
                    this.fogGraphics.endFill();
                }
                
                this.lastFogIntensity = fogIntensity;
            }
        }

                 updateSunAnimation() {
             if (this.sunSprite && this.solarFlares.length > 0) {
                // Performance optimization: only update sun texture every few frames
                if (this.animationTimer - this.lastSunUpdate < this.sunUpdateInterval) {
                    return; // Use cached texture
                }
                this.lastSunUpdate = this.animationTimer;
                
                 // Create animated sun texture
                 const sunSize = 150;
                 const canvasSize = 800;
                 const canvas = document.createElement('canvas');
                 canvas.width = canvasSize;
                 canvas.height = canvasSize;
                 const ctx = canvas.getContext('2d');
                 
                 const centerX = canvasSize / 2;
                 const centerY = canvasSize / 2;
                 
                 // Draw base sun (same as before)
                 const coreGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, sunSize * 0.3);
                 coreGradient.addColorStop(0, '#FFFFFF');
                 coreGradient.addColorStop(0.5, '#FFFFE0');
                 coreGradient.addColorStop(1, '#FFFF00');
                 
                 const photosphereGradient = ctx.createRadialGradient(centerX, centerY, sunSize * 0.2, centerX, centerY, sunSize);
                 photosphereGradient.addColorStop(0, '#FFFF00');
                 photosphereGradient.addColorStop(0.6, '#FFD700');
                 photosphereGradient.addColorStop(1, '#FFA500');
                 
                 // Draw photosphere
                 ctx.fillStyle = photosphereGradient;
                 ctx.beginPath();
                 ctx.arc(centerX, centerY, sunSize, 0, Math.PI * 2);
                 ctx.fill();
                 
                 // Draw core
                 ctx.fillStyle = coreGradient;
                 ctx.beginPath();
                 ctx.arc(centerX, centerY, sunSize * 0.3, 0, Math.PI * 2);
                 ctx.fill();
                 
                 // Add animated solar granules
                 ctx.fillStyle = 'rgba(255, 255, 0, 0.3)';
                 for (let i = 0; i < 50; i++) {
                     const angle = Math.random() * Math.PI * 2;
                     const radius = Math.random() * sunSize * 0.8;
                     const x = centerX + Math.cos(angle) * radius;
                     const y = centerY + Math.sin(angle) * radius;
                     const granuleSize = 2 + Math.random() * 8;
                     
                     ctx.beginPath();
                     ctx.arc(x, y, granuleSize, 0, Math.PI * 2);
                     ctx.fill();
                 }
                 
                 // Draw animated solar flares with movement and rotation
                 for (let flare of this.solarFlares) {
                     flare.phase += flare.speed;
                     
                     // Add movement to flares
                     if (!flare.offsetX) flare.offsetX = 0;
                     if (!flare.offsetY) flare.offsetY = 0;
                     if (!flare.rotation) flare.rotation = 0;
                     
                     flare.offsetX += Math.sin(flare.phase * 0.5) * 0.5;
                     flare.offsetY += Math.cos(flare.phase * 0.3) * 0.3;
                     flare.rotation += 0.02;
                     
                     const pulse = 0.8 + 0.4 * Math.sin(flare.phase);
                     const currentSize = flare.baseSize * pulse;
                     
                     // Calculate new position with movement
                     const newX = flare.x + flare.offsetX;
                     const newY = flare.y + flare.offsetY;
                     
                     // Create dynamic solar flares with rotation
                     ctx.save();
                     ctx.translate(newX, newY);
                     ctx.rotate(flare.rotation);
                     
                     // Main flare body
                     ctx.fillStyle = `rgba(255, 200, 0, ${0.4 + 0.3 * pulse})`;
                     ctx.beginPath();
                     ctx.ellipse(0, 0, currentSize, currentSize * 0.6, 0, 0, Math.PI * 2);
                     ctx.fill();
                     
                     // Flare glow with varying intensity
                     ctx.fillStyle = `rgba(255, 150, 0, ${0.15 + 0.1 * pulse})`;
                     ctx.beginPath();
                     ctx.ellipse(0, 0, currentSize * 1.8, currentSize * 1.2, 0, 0, Math.PI * 2);
                     ctx.fill();
                     

                     
                     ctx.restore();
                 }
                 
                 // Draw animated sunspots with movement and shape changes
                 for (let spot of this.sunspots) {
                     spot.phase += spot.speed;
                     
                     // Add movement to sunspots
                     if (!spot.offsetX) spot.offsetX = 0;
                     if (!spot.offsetY) spot.offsetY = 0;
                     if (!spot.rotation) spot.rotation = 0;
                     
                     spot.offsetX += Math.sin(spot.phase * 0.7) * 0.3;
                     spot.offsetY += Math.cos(spot.phase * 0.5) * 0.2;
                     spot.rotation += 0.01;
                     
                     const pulse = 0.7 + 0.3 * Math.sin(spot.phase);
                     const currentSize = spot.baseSize * pulse;
                     
                     // Calculate new position with movement
                     const newX = spot.x + spot.offsetX;
                     const newY = spot.y + spot.offsetY;
                     
                     // Create dynamic sunspots with rotation
                     ctx.save();
                     ctx.translate(newX, newY);
                     ctx.rotate(spot.rotation);
                     
                     // Main sunspot (darker area)
                     ctx.fillStyle = `rgba(200, 100, 0, ${0.25 + 0.15 * pulse})`;
                     ctx.beginPath();
                     ctx.ellipse(0, 0, currentSize, currentSize * 0.8, 0, 0, Math.PI * 2);
                     ctx.fill();
                     
                     // Sunspot penumbra (lighter edge)
                     ctx.fillStyle = `rgba(220, 120, 0, ${0.1 + 0.05 * pulse})`;
                     ctx.beginPath();
                     ctx.ellipse(0, 0, currentSize * 1.3, currentSize * 1.1, 0, 0, Math.PI * 2);
                     ctx.fill();
                     
                     // Add sunspot detail lines
                     for (let i = 0; i < 2; i++) {
                         const lineAngle = (Math.PI * i) / 2 + spot.rotation;
                         const lineLength = currentSize * 0.6;
                         const lineX = Math.cos(lineAngle) * lineLength;
                         const lineY = Math.sin(lineAngle) * lineLength;
                         
                         ctx.strokeStyle = `rgba(180, 80, 0, ${0.2 + 0.1 * pulse})`;
                         ctx.lineWidth = 1;
                         ctx.lineCap = 'round';
                         ctx.beginPath();
                         ctx.moveTo(-lineX, -lineY);
                         ctx.lineTo(lineX, lineY);
                         ctx.stroke();
                     }
                     
                     ctx.restore();
                 }
                 
                 // Add outer corona glow
                 const coronaGradient = ctx.createRadialGradient(centerX, centerY, sunSize, centerX, centerY, sunSize * 2);
                 coronaGradient.addColorStop(0, 'rgba(255, 165, 0, 0.2)');
                 coronaGradient.addColorStop(0.5, 'rgba(255, 165, 0, 0.1)');
                 coronaGradient.addColorStop(1, 'rgba(255, 165, 0, 0)');
                 
                 ctx.fillStyle = coronaGradient;
                 ctx.beginPath();
                 ctx.arc(centerX, centerY, sunSize * 2, 0, Math.PI * 2);
                 ctx.fill();
                 
                 // Add animated corona rays
                 for (let i = 0; i < 12; i++) {
                     const angle = (Math.PI * 2 * i) / 12;
                     const rayPhase = this.animationTimer * 0.02 + i * 0.5;
                     const rayPulse = 0.7 + 0.3 * Math.sin(rayPhase);
                     const rayLength = sunSize * (1.2 + Math.sin(rayPhase * 0.5) * 0.3);
                     
                     const startX = centerX + Math.cos(angle) * (sunSize * 0.8);
                     const startY = centerY + Math.sin(angle) * (sunSize * 0.8);
                     const endX = centerX + Math.cos(angle) * rayLength;
                     const endY = centerY + Math.sin(angle) * rayLength;
                     
                     // Create animated ray gradient
                     const rayGradient = ctx.createLinearGradient(startX, startY, endX, endY);
                     rayGradient.addColorStop(0, `rgba(255, 255, 255, ${0.8 * rayPulse})`);
                     rayGradient.addColorStop(0.3, `rgba(255, 255, 0, ${0.6 * rayPulse})`);
                     rayGradient.addColorStop(0.7, `rgba(255, 200, 0, ${0.4 * rayPulse})`);
                     rayGradient.addColorStop(1, `rgba(255, 150, 0, ${0.1 * rayPulse})`);
                     
                     ctx.strokeStyle = rayGradient;
                     ctx.lineWidth = 3 + Math.sin(rayPhase * 2) * 2;
                     ctx.lineCap = 'round';
                     
                     ctx.beginPath();
                     ctx.moveTo(startX, startY);
                     ctx.lineTo(endX, endY);
                     ctx.stroke();
                 }
                 
                 // Add secondary animated corona rays
                 for (let i = 0; i < 8; i++) {
                     const angle = (Math.PI * 2 * i) / 8 + Math.PI / 8;
                     const rayPhase = this.animationTimer * 0.015 + i * 0.7;
                     const rayPulse = 0.6 + 0.4 * Math.sin(rayPhase);
                     const rayLength = sunSize * (0.9 + Math.sin(rayPhase * 0.7) * 0.2);
                     
                     const startX = centerX + Math.cos(angle) * (sunSize * 0.9);
                     const startY = centerY + Math.sin(angle) * (sunSize * 0.9);
                     const endX = centerX + Math.cos(angle) * rayLength;
                     const endY = centerY + Math.sin(angle) * rayLength;
                     
                     // Create animated secondary ray gradient
                     const rayGradient = ctx.createLinearGradient(startX, startY, endX, endY);
                     rayGradient.addColorStop(0, `rgba(255, 255, 255, ${0.5 * rayPulse})`);
                     rayGradient.addColorStop(0.5, `rgba(255, 200, 0, ${0.3 * rayPulse})`);
                     rayGradient.addColorStop(1, `rgba(255, 150, 0, ${0.05 * rayPulse})`);
                     
                     ctx.strokeStyle = rayGradient;
                     ctx.lineWidth = 1 + Math.sin(rayPhase * 3) * 1;
                     ctx.lineCap = 'round';
                     
                     ctx.beginPath();
                     ctx.moveTo(startX, startY);
                     ctx.lineTo(endX, endY);
                     ctx.stroke();
                 }
                 

                 
                 // Update sun texture
                 this.sunSprite.texture = PIXI.Texture.from(canvas);
             }
         }

         updateAudio() {
             // Audio removed for silent eclipse
         }

        // Easing function for smooth moon movement with slow center
        easeInOutQuad(t) {
            // Start normal, slow down in center, speed up at end
            if (t < 0.25) {
                // First 25%: normal speed approach
                return t * 0.4; // Normal linear movement
            } else if (t > 0.75) {
                // Last 25%: normal speed exit
                const exitT = (t - 0.75) / 0.25;
                return 0.6 + (exitT * 0.4); // Normal linear movement
            } else {
                // Middle 50%: very slow movement through center
                const centerT = (t - 0.25) / 0.5;
                return 0.1 + (centerT * 0.5); // Very slow movement
            }
        }
        
        // Easing function for natural vein growth
        easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        }
        
        // Draw organic curved vein with variable thickness
        drawCurvedVein(graphics, startX, startY, angle, length, maxWidth, alpha, seed) {
            // Create curved path with multiple segments
            const segments = 8;
            const points = [];
            
            // Generate control points for smooth curve
            for (let i = 0; i <= segments; i++) {
                const t = i / segments;
                const segmentLength = length * t;
                
                // Add organic curve variation
                const curveOffset = Math.sin(t * Math.PI * 2 + seed * 0.5) * 8;
                const curveAngle = angle + Math.sin(t * Math.PI * 3 + seed * 0.3) * 0.2;
                
                const x = startX + Math.cos(curveAngle) * segmentLength + Math.cos(angle + Math.PI / 2) * curveOffset;
                const y = startY + Math.sin(curveAngle) * segmentLength + Math.sin(angle + Math.PI / 2) * curveOffset;
                
                points.push({ x, y, t });
            }
            
            // Draw vein with variable thickness
            for (let i = 0; i < points.length - 1; i++) {
                const current = points[i];
                const next = points[i + 1];
                
                // Calculate variable thickness (thick at base, thin at tip)
                const thickness = maxWidth * (1 - current.t * 0.7); // Taper to 30% at tip
                const currentAlpha = alpha * (1 - current.t * 0.3); // Slight fade at tip
                
                // Add subtle color variation
                const colorVariation = Math.sin(current.t * Math.PI + seed) * 0.1;
                const redComponent = Math.floor(255 * (0.8 + colorVariation));
                const color = (redComponent << 16) | (0 << 8) | 0; // Dark red to black
                
                graphics.lineStyle(thickness, color, currentAlpha);
                graphics.moveTo(current.x, current.y);
                graphics.lineTo(next.x, next.y);
            }
            
            // Add subtle "nodes" at branching points
            if (seed % 3 === 0) {
                const nodeX = points[Math.floor(points.length * 0.7)].x;
                const nodeY = points[Math.floor(points.length * 0.7)].y;
                graphics.beginFill(0x000000, alpha * 0.8);
                graphics.drawCircle(nodeX, nodeY, maxWidth * 0.3);
                graphics.endFill();
            }
            
            return { endX: points[points.length - 1].x, endY: points[points.length - 1].y };
        }
        
        // Draw wriggling vein with enhanced organic movement - optimized and freaky
        drawWrigglingVein(graphics, startX, startY, angle, length, maxWidth, alpha, seed) {
            // Optimized: fewer segments for better performance
            const segments = 8;
            const points = [];
            
            // Generate control points with enhanced wriggling
            for (let i = 0; i <= segments; i++) {
                const t = i / segments;
                const segmentLength = length * t;
                
                // Reduced wriggling with fewer sine waves - less squiggly
                const wriggle1 = Math.sin(t * Math.PI * 2 + this.animationTimer * 0.08 + seed * 0.3) * 6;
                const wriggle2 = Math.sin(t * Math.PI * 3 + this.animationTimer * 0.12 + seed * 0.7) * 3;
                const totalWriggle = wriggle1 + wriggle2;
                
                // Reduced angle variation - less erratic
                const angleVariation = Math.sin(t * Math.PI * 2 + this.animationTimer * 0.06 + seed * 0.5) * 0.2;
                const currentAngle = angle + angleVariation;
                
                const x = startX + Math.cos(currentAngle) * segmentLength + Math.cos(angle + Math.PI / 2) * totalWriggle;
                const y = startY + Math.sin(currentAngle) * segmentLength + Math.sin(angle + Math.PI / 2) * totalWriggle;
                
                points.push({ x, y, t });
            }
            
            // Draw vein with variable thickness and pulsing - more freaky
            for (let i = 0; i < points.length - 1; i++) {
                const current = points[i];
                const next = points[i + 1];
                
                        // Calculate variable thickness with enhanced pulsing - improved visibility
        const baseThickness = maxWidth * (1 - current.t * 0.4); // Less taper for more prominent look
        const pulse = Math.sin(this.animationTimer * 0.25 + seed * 0.5 + current.t * Math.PI * 2) * 0.3 + 0.7; // More stable pulse
        const thickness = baseThickness * pulse;
        const currentAlpha = alpha * (1 - current.t * 0.1); // Minimal fade for maximum visibility
                
                // Enhanced color variation with hot red shifts
                const colorVariation = Math.sin(current.t * Math.PI * 2 + seed + this.animationTimer * 0.15) * 0.15;
                const redComponent = Math.floor(255 * (0.8 + colorVariation)); // Hot red base
                const color = (redComponent << 16) | (0 << 8) | 0; // Hot red to bright red
                
                graphics.lineStyle(thickness, color, currentAlpha);
                graphics.moveTo(current.x, current.y);
                graphics.lineTo(next.x, next.y);
            }
            
            // Add animated "nodes" at branching points - more freaky
            if (seed % 2 === 0) { // More frequent nodes
                const nodeX = points[Math.floor(points.length * 0.6)].x;
                const nodeY = points[Math.floor(points.length * 0.6)].y;
                const nodePulse = Math.sin(this.animationTimer * 0.4 + seed) * 0.3 + 0.7; // More dramatic pulse
                const nodeSize = maxWidth * 0.4 * nodePulse; // Larger nodes
                graphics.beginFill(0x000000, alpha * 0.9 * nodePulse);
                graphics.drawCircle(nodeX, nodeY, nodeSize);
                graphics.endFill();
                
                // Add "eyes" to some nodes for extra freaky effect
                if (seed % 4 === 0) {
                    const eyeSize = nodeSize * 0.3;
                    graphics.beginFill(0xFF0000, alpha * 0.8);
                    graphics.drawCircle(nodeX - eyeSize, nodeY - eyeSize, eyeSize);
                    graphics.drawCircle(nodeX + eyeSize, nodeY - eyeSize, eyeSize);
                    graphics.endFill();
                }
            }
            
            return { endX: points[points.length - 1].x, endY: points[points.length - 1].y };
        }
        
        updateBaileysBeads() {
            // Baily's Beads appear during totality (when moon covers sun)
            if (this.progress > 0.45 && this.progress < 0.55) {
                const beadIntensity = Math.sin((this.progress - 0.45) * Math.PI * 10) * 0.5 + 0.5;
                
                // Create random bright spots around moon's edge (more realistic)
                const beadCount = 5 + Math.floor(Math.random() * 4); // 5-8 beads
                for (let i = 0; i < beadCount; i++) {
                    // Random angle around the moon's edge
                    const angle = Math.random() * Math.PI * 2;
                    const distance = 140 + (Math.random() - 0.5) * 20; // Vary distance slightly
                    const beadX = this.moonSprite.x + Math.cos(angle) * distance;
                    const beadY = this.moonSprite.y + Math.sin(angle) * distance;
                    
                    // Create bright bead effect
                    const bead = new PIXI.Graphics();
                    bead.beginFill(0xFFFFFF, beadIntensity * 0.9);
                    bead.drawCircle(0, 0, 2 + Math.random() * 3);
                    bead.endFill();
                    bead.x = beadX;
                    bead.y = beadY;
                    
                    this.container.addChild(bead);
                    this.activeGraphics.add(bead);
                    
                    // Remove bead after a short time
                    this.safeSetTimeout(() => {
                        if (this.container && this.container.children.includes(bead)) {
                            this.container.removeChild(bead);
                        }
                        this.activeGraphics.delete(bead);
                    }, 150);
                }
            }
        }
        
        updateDiamondRing() {
            // Diamond Ring effect when moon first/last touches sun
            if (this.progress > 0.48 && this.progress < 0.52) {
                const ringIntensity = Math.sin((this.progress - 0.48) * Math.PI * 25) * 0.5 + 0.5;
                
                // Add natural flickering effect
                const flicker = 0.7 + 0.3 * Math.sin(this.animationTimer * 0.3) * Math.sin(this.animationTimer * 0.7);
                const flickerIntensity = ringIntensity * flicker;
                
                // Position diamond ring on the right edge of the moon (no movement)
                const moonRadius = 140;
                const ringX = this.moonSprite.x + moonRadius;
                const ringY = this.moonSprite.y;
                
                // Create bright diamond ring (concentrated point with ray)
                const ring = new PIXI.Graphics();
                
                // Main bright diamond point
                ring.beginFill(0xFFFFFF, flickerIntensity * 0.9);
                ring.drawCircle(0, 0, 8); // Small, concentrated bright point
                ring.endFill();
                
                // Bright core glow
                ring.beginFill(0xFFFF00, flickerIntensity * 0.6);
                ring.drawCircle(0, 0, 15);
                ring.endFill();
                
                // Animated extending ray (horizontal starburst effect)
                const rayLength = 60 + Math.sin(this.animationTimer * 0.4) * 20; // Animated length
                const rayWidth = 2 + Math.sin(this.animationTimer * 0.6) * 1; // Animated width
                
                ring.beginFill(0xFFFFFF, flickerIntensity * 0.4);
                ring.drawRect(-rayLength, -rayWidth, rayLength * 2, rayWidth * 2);
                ring.endFill();
                
                // Additional animated ray segments for starburst effect
                const innerRayLength = 40 + Math.sin(this.animationTimer * 0.5) * 15;
                ring.beginFill(0xFFFF00, flickerIntensity * 0.3);
                ring.drawRect(-innerRayLength, -1, innerRayLength * 2, 2);
                ring.endFill();
                
                // Animated vertical ray for cross effect
                const vertRayLength = 20 + Math.sin(this.animationTimer * 0.3) * 10;
                ring.beginFill(0xFFFFFF, flickerIntensity * 0.3);
                ring.drawRect(-2, -vertRayLength, 4, vertRayLength * 2);
                ring.endFill();
                
                ring.x = ringX;
                ring.y = ringY;
                
                // Add animated rotation
                ring.rotation = Math.sin(this.animationTimer * 0.1) * 0.1; // Subtle rotation
                
                this.container.addChild(ring);
                this.activeGraphics.add(ring);
                
                // Remove ring after short time
                this.safeSetTimeout(() => {
                    if (this.container && this.container.children.includes(ring)) {
                        this.container.removeChild(ring);
                    }
                    this.activeGraphics.delete(ring);
                }, 100);
            }
        }
        
        updateCameraEffects() {
            // Continuous zoom throughout the entire eclipse duration
            const zoomLevel = 1 + this.progress * 0.8; // Zoom in up to 80% over full duration
            this.cameraZoom = zoomLevel;
            
            // Set pivot to center for proper zoom
            this.container.pivot.set(Graphics.width / 2, Graphics.height / 2);
            this.container.position.set(Graphics.width / 2, Graphics.height / 2);
            
            // Apply zoom to container
            this.container.scale.set(this.cameraZoom);
        }
        
        updateBloodMoonTexture(progress) {
            // Add blood moon texture effects to the sun
            if (this.sunSprite) {
                // Create separate graphics objects for different layers
                const backgroundEffects = new PIXI.Graphics();
                const veinLayer = new PIXI.Graphics();
                const pupilLayer = new PIXI.Graphics();
                
                // Add hot red energy rings - coordinated with veins (background layer)
                for (let i = 0; i < 3; i++) { // Fewer rings for cleaner look
                    const waveRadius = 50 + (i * 35) + Math.sin(this.animationTimer * 0.1 + i) * 12; // Much bigger rings
                    const waveAlpha = (0.6 - i * 0.15) * progress; // Higher opacity for visibility
                    
                    backgroundEffects.lineStyle(2, 0xCC0000, waveAlpha); // Darker red, thinner lines
                    backgroundEffects.drawCircle(0, 0, waveRadius);
                }
                
                // Add pulsing hot red orbs - coordinated with vein nodes (background layer)
                for (let i = 0; i < 3; i++) { // Reduced count for cleaner look
                    // Position orbs near vein branching points
                    const angle = (Math.PI * 2 * i) / 3 + this.animationTimer * 0.008; // Slower rotation
                    const radius = 60 + Math.sin(this.animationTimer * 0.04 + i) * 15; // Smaller radius
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius;
                    const orbSize = 4 + Math.sin(this.animationTimer * 0.2 + i) * 2; // Larger, more visible
                    
                    backgroundEffects.beginFill(0xCC0000, 0.8 * progress); // Darker red, higher opacity
                    backgroundEffects.drawCircle(x, y, orbSize);
                    backgroundEffects.endFill();
                    
                    // Add subtle glow
                    backgroundEffects.beginFill(0xAA0000, 0.4 * progress); // Darker glow
                    backgroundEffects.drawCircle(x, y, orbSize * 1.5);
                    backgroundEffects.endFill();
                }
                

                

                
                // Add animated black vein structures using stored random paths (vein layer on top)
                console.log('Drawing veins with progress:', progress); // Debug log
                
                // Draw organic curved veins with coordinated growth timing - optimized frame skip
                if (this.animationTimer % 3 === 0) { // Update every 3 frames for better performance
                    for (let i = 0; i < this.veinPaths.length; i++) {
                        const veinPath = this.veinPaths[i];
                        const angle = veinPath.mainAngle;
                        
                        // Growing veins - coordinated with blood moon progress
                        const baseLength = 25;
                        const growthProgress = Math.min(1, (this.animationTimer - i * 8) / 80); // Slower, more coordinated growth
                        const veinLength = baseLength + (growthProgress * 45); // Grow from 25 to 70 pixels
                        
                        // Create curved main vein with variable thickness and wriggling - improved visibility
                        this.drawWrigglingVein(veinLayer, 0, 0, angle, veinLength, 5, 1.0, i); // Thicker, fully opaque
                        
                        // Draw secondary branches with natural clustering - only if main vein has grown enough
                        if (growthProgress > 0.4) {
                            for (let j = 0; j < veinPath.branchCount; j++) {
                                const branch = veinPath.branches[j];
                                
                                // Use stored random branch angle
                                const goldenAngle = Math.PI * 0.618; // Golden ratio
                                const baseBranchAngle = angle + goldenAngle * (j - (veinPath.branchCount - 1) / 2);
                                const branchAngle = baseBranchAngle + branch.angleOffset + Math.sin(this.animationTimer * 0.006 + i + j) * 0.3; // Add slight animation
                                
                                // Growing branches with coordinated timing
                                const branchGrowthProgress = Math.min(1, (this.animationTimer - (i * 8 + j * 12)) / 50);
                                const branchLength = 35 + (branchGrowthProgress * 35); // Grow from 35 to 70 pixels
                                const branchStartX = Math.cos(angle) * (veinLength * 0.7); // Start 70% along main vein
                                const branchStartY = Math.sin(angle) * (veinLength * 0.7);
                                
                                // Draw curved secondary branch with wriggling - improved visibility
                                this.drawWrigglingVein(veinLayer, branchStartX, branchStartY, branchAngle, branchLength, 3, 0.9, i + j); // Thicker, higher opacity
                                
                                // Draw tertiary branches with asymmetrical growth - only if branch has grown enough
                                if (branchGrowthProgress > 0.6) {
                                    for (let k = 0; k < branch.subBranchCount; k++) {
                                        const subBranch = branch.subBranches[k];
                                        
                                        // Use stored random sub-branch angle
                                        const subAngle = branchAngle + (Math.PI / 3) * (k - 0.5) + subBranch.angleOffset + Math.sin(this.animationTimer * 0.006 + i + j + k) * 0.2; // Add slight animation
                                        const subGrowthProgress = Math.min(1, (this.animationTimer - (i * 8 + j * 12 + k * 18)) / 35);
                                        const subLength = 20 + (subGrowthProgress * 25); // Grow from 20 to 45 pixels
                                        const subStartX = branchStartX + Math.cos(branchAngle) * (branchLength * 0.6);
                                        const subStartY = branchStartY + Math.sin(branchAngle) * (branchLength * 0.6);
                                        
                                        // Draw curved tertiary branch with wriggling - improved visibility
                                        this.drawWrigglingVein(veinLayer, subStartX, subStartY, subAngle, subLength, 2, 0.8, i + j + k); // Thicker, higher opacity
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Add evil slit pupil with freaky effects (pupil layer on top of everything)
                const pupilGrowthFactor = 1 + (this.animationTimer * 0.025); // Slightly faster growth
                const basePupilWidth = 0.4;
                const basePupilHeight = 1.25;
                const pupilWidth = basePupilWidth * pupilGrowthFactor;
                const pupilHeight = basePupilHeight * pupilGrowthFactor;
                const pupilAlpha = 0.95 * progress;
                
                // Hot red glow around pupil - pulsing
                const glowPulse = Math.sin(this.animationTimer * 0.3) * 0.2 + 0.8;
                pupilLayer.beginFill(0xFF0000, pupilAlpha * 0.8 * glowPulse); // Hot red, higher opacity
                pupilLayer.drawEllipse(0, 0, pupilWidth * 2.5, pupilHeight * 2);
                pupilLayer.endFill();
                
                // Dark pupil core with "breathing" effect
                const breathPulse = Math.sin(this.animationTimer * 0.4) * 0.15 + 0.85;
                pupilLayer.beginFill(0x000000, pupilAlpha * breathPulse);
                pupilLayer.drawEllipse(0, 0, pupilWidth, pupilHeight);
                pupilLayer.endFill();
                
                // Darker inner pupil for depth
                pupilLayer.beginFill(0x000000, pupilAlpha);
                pupilLayer.drawEllipse(0, 0, pupilWidth * 0.5, pupilHeight * 0.7);
                pupilLayer.endFill();
                

                
                // Add layers to sun sprite with proper z-index ordering
                // Background effects first (lowest z-index)
                backgroundEffects.zIndex = 1000;
                this.sunSprite.addChild(backgroundEffects);
                this.activeGraphics.add(backgroundEffects);
                
                // Vein layer on top of background effects
                veinLayer.zIndex = 1001;
                this.sunSprite.addChild(veinLayer);
                this.activeGraphics.add(veinLayer);
                
                // Pupil layer on top of everything
                pupilLayer.zIndex = 1002;
                this.sunSprite.addChild(pupilLayer);
                this.activeGraphics.add(pupilLayer);
                
                // Remove overlays after a longer time to ensure visibility
                this.safeSetTimeout(() => {
                    if (this.sunSprite && this.sunSprite.children.includes(backgroundEffects)) {
                        this.sunSprite.removeChild(backgroundEffects);
                    }
                    if (this.sunSprite && this.sunSprite.children.includes(veinLayer)) {
                        this.sunSprite.removeChild(veinLayer);
                    }
                    if (this.sunSprite && this.sunSprite.children.includes(pupilLayer)) {
                        this.sunSprite.removeChild(pupilLayer);
                    }
                    this.activeGraphics.delete(backgroundEffects);
                    this.activeGraphics.delete(veinLayer);
                    this.activeGraphics.delete(pupilLayer);
                }, 500); // Increased from 200ms to 500ms
            }
        }
    }

    // Eclipse Scene Class
    class Scene_BloodmoonEclipse extends Scene_Base {
        constructor() {
            super();
            this.eclipse = null;
            this.background = null;
            this.ui = null;
            this.timer = 0;
            this.duration = DURATION;
            this.intensity = INTENSITY;
        }

                 create() {
             super.create();
             this.createBackground();
             this.createEclipse();
             this.startEclipse();
         }

        createBackground() {
            // Create dynamic sky background
            this.background = new PIXI.Container();
            
            // Sky background (will change color during eclipse)
            this.skyBackground = new PIXI.Graphics();
            // Use exact same RGB values as the update logic: Red=135, Green=206, Blue=235
            const skyBlue = (135 << 16) | (206 << 8) | 235;
            this.skyBackground.beginFill(skyBlue);
            this.skyBackground.drawRect(0, 0, Graphics.width, Graphics.height);
            this.skyBackground.endFill();
            this.background.addChild(this.skyBackground);
            
            this.addChild(this.background);
        }

                 createEclipse() {
             this.eclipse = new BloodmoonEclipse();
             this.eclipse.scene = this;
             this.addChild(this.eclipse.container);
             this.addChild(this.eclipse.fogContainer);
         }

        createUI() {
            // UI removed for clean visual experience
            this.ui = null;
        }

        startEclipse() {
            this.eclipse.start();
            this.timer = 0;
        }

        update() {
            super.update();
            
            if (this.eclipse && this.eclipse.active) {
                this.eclipse.update();
                this.timer++;
                
                // Check for completion - wait for moon to complete its arc
                const eclipseProgress = this.timer / this.duration;
                const screenWidth = Graphics.width;
                const moonSize = 140;
                const endX = screenWidth + moonSize;
                
                // Calculate moon's current X position
                const startX = -moonSize;
                const moonX = startX + (endX - startX) * eclipseProgress;
                
                // Update sky color first
                this.updateSkyColor();
                
                // Then exit when moon has moved completely off-screen
                if (moonX >= endX) {
                    this.completeEclipse();
                }
            }
        }

        updateUI() {
            // UI removed for clean visual experience
        }

        updateSkyColor() {
            if (this.skyBackground) {
                let skyColor;
                // Use eclipse progress to sync with moon position
                const eclipseProgress = this.timer / this.duration;
                
                if (eclipseProgress <= 0.5) {
                    // First half: sky blue to black
                    const progress = eclipseProgress * 2; // 0 to 1
                    const redComponent = Math.floor(135 * (1 - progress)); // 87 -> 0
                    const greenComponent = Math.floor(206 * (1 - progress)); // CE -> 0
                    const blueComponent = Math.floor(235 * (1 - progress)); // EB -> 0
                    skyColor = (redComponent << 16) | (greenComponent << 8) | blueComponent;
                } else {
                    // Second half: black to purple
                    const progress = (eclipseProgress - 0.5) * 2; // 0 to 1
                    const redComponent = Math.floor(150 * progress); // 0 -> 150 (purple red)
                    const greenComponent = Math.floor(50 * progress); // 0 -> 50 (dark green)
                    const blueComponent = Math.floor(200 * progress); // 0 -> 200 (purple blue)
                    skyColor = (redComponent << 16) | (greenComponent << 8) | blueComponent;
                }
                
                // Don't force purple at the end - let it stay at the calculated color
                // This prevents the purple flash when scene exits
                
                this.skyBackground.clear();
                this.skyBackground.beginFill(skyColor);
                this.skyBackground.drawRect(0, 0, Graphics.width, Graphics.height);
                this.skyBackground.endFill();
            }
        }

        handleInput() {
            // Input handling removed for clean visual experience
        }

        completeEclipse() {
            // Auto-exit immediately after completion
            this.exitEclipse();
        }

        exitEclipse() {
            try {
                if (this.eclipse) {
                    this.eclipse.stop();
                }
                SceneManager.pop();
            } catch (e) {
                console.log('Error exiting eclipse:', e);
                // Force pop the scene even if there's an error
                SceneManager.pop();
            }
        }
    }

    // Plugin functions
    function startEclipse() {
        SceneManager.push(Scene_BloodmoonEclipse);
    }

    function stopEclipse() {
        if (SceneManager._scene instanceof Scene_BloodmoonEclipse) {
            SceneManager.pop();
        }
    }

    // Add to global scope for external access
    window.BloodmoonEclipse = {
        start: startEclipse,
        stop: stopEclipse,
        getInstance: () => SceneManager._scene instanceof Scene_BloodmoonEclipse ? SceneManager._scene.eclipse : null
    };

})(); 

