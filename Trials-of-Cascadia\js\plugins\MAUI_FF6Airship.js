/*:
 * @target MZ
 * @plugindesc [v1.04] Adds FF6-style airship controls using UltraMode7 — yaw turning, pitch-based altitude, custom parallax effects, and ground trail at low altitude.
 * <AUTHOR> <PERSON><PERSON> Studio
 * 
 * @help MAUI_FF6Airship.js
 *
 * This plugin enhances UltraMode7 by adding Final Fantasy VI-style airship features.
 * 
 * 🕹️ Airship Controls:
 * - Left/Right  = Turn (yaw)
 * - Up/Down     = Adjust altitude
 * - Shift/X     = Fly forward
 * - Enter/A     = Land
 *
 * 🌪️ Ground Trail Effect:
 * - When flying at low altitude (≤120), the airship leaves a dynamic dust trail on the ground
 * - Trail intensity, size, and speed scale dramatically with altitude
 * - Trail points fade out over time and are automatically cleaned up
 *
 * 🔧 Required Plugins (load in this order):
 * - UltraMode7.js by <PERSON> "Blizzard" Mikić
 * - MAUI_FF6Airship.js
 *
 * ✨ NEW: No longer requires SAN_AnalogMovement!
 * This plugin now includes its own custom 360-degree movement system.
 *
 * ⚠️ Note:
 * While the required plugins are free for commercial use, this plugin is also free to use in commercial projects.
 *
 * 🛠️ Usage Instructions:
 * This plugin overrides several UltraMode7 settings while in an airship:
 * - UltraMode7.CHARACTERS_ADJUST_SPRITE_DIRECTION
 * - UltraMode7.FADE_Z_END
 * - UltraMode7.DEFAULT_FAR_CLIP_Z
 * - UltraMode7.DEFAULT_PARALLAX_DISTANCE
 * - UltraMode7.DEFAULT_CAMERA_Y
 * 
 * To activate UltraMode7 on your world map, add the following tag to the map's note field:
 *   <UltraMode7>
 * For night maps adjust the fade effect color:
 *   <UltraMode7_FadeColor:1,1,1>
 * 
 * A sample parallax image (mauiparallax.png) is included. Use it as a template if you'd like to make your own parallax backgrounds.
 *
 * Questions? Join my Discord:
 * https://discord.com/invite/g2ksjJx
 *
 * Version: 1.04
 *
 * 1.01 - 7.20	Erased accidental line update that was making game run at double speed.
 *				Thank you Lexiand for reporting!
 * 1.02 - 7.21	Now when landing the Yaw turnes left or right, depending on which is closer.
 * 1.03 - 7.24  Fixed issue that locked player looking left, Thanks Alasthorn.
 * 1.04 - 12.19 Added ground trail effect when airship flies at low altitude (≤120).
 *
 */


(() => {
	
	// Plugin compatibility and error checking system
	const PLUGIN_NAME = 'MAUI_FF6Airship';
	const REQUIRED_PLUGINS = ['UltraMode7'];

	// OPTIMIZATION: Debug mode flag to reduce console logging overhead
	const DEBUG_MODE = true; // Set to true only when debugging - TEMPORARILY ENABLED

	// Check for required plugins
	function checkRequiredPlugins() {
	  const missingPlugins = [];
	  REQUIRED_PLUGINS.forEach(pluginName => {
	    if (!PluginManager.parameters(pluginName)) {
	      missingPlugins.push(pluginName);
	    }
	  });

	  if (missingPlugins.length > 0) {
	    if (DEBUG_MODE) {
	    console.error(`❌ ${PLUGIN_NAME}: Missing required plugins: ${missingPlugins.join(', ')}`);
	    console.error(`❌ ${PLUGIN_NAME}: Plugin will not function correctly without these dependencies.`);
	    }
	    return false;
	  }

	  if (DEBUG_MODE) console.log(`✅ ${PLUGIN_NAME}: All required plugins found`);
	  return true;
	}

	// Initialize plugin compatibility check
	if (!checkRequiredPlugins()) {
	  if (DEBUG_MODE) console.error(`❌ ${PLUGIN_NAME}: Plugin initialization failed due to missing dependencies.`);
	  return;
	}

	// Solution: Dynamically load/unload SAN_AnalogMovement plugin
	// This completely enables/disables the plugin instead of trying to modify its behavior

	let _sanAnalogMoveLoaded = false;
	let _sanAnalogMoveScript = null;

	// Function to dynamically load SAN_AnalogMovement
	function loadSANAnalogMovement() {
	  if (_sanAnalogMoveLoaded) {
	    if (DEBUG_MODE) console.log('✈️ SAN_AnalogMove already loaded');
	    return;
	  }

	  // Enable the plugin in the plugins list
	  const plugins = $plugins || [];
	  const sanPlugin = plugins.find(plugin => plugin.name === 'SAN_AnalogMove');
	  if (sanPlugin) {
	    sanPlugin.status = true;
	  }

	  // Load the script dynamically
	  const script = document.createElement('script');
	  script.type = 'text/javascript';
	  script.src = 'js/plugins/SAN_AnalogMove.js';
	  script.async = false;

	  script.onload = function() {
	    _sanAnalogMoveLoaded = true;
	    if (DEBUG_MODE) console.log('✈️ SAN_AnalogMove loaded successfully');

	    // Initialize the plugin if it has an initialization function
	    if (typeof Sanshiro !== 'undefined' && Sanshiro.AnalogMove) {
	      Sanshiro.AnalogMove.enabled = true;
	      if (DEBUG_MODE) console.log('✈️ SAN_AnalogMove enabled');
	    }

	    // CRITICAL: Reinitialize player and followers with movers
	    if ($gamePlayer && typeof $gamePlayer.initMover === 'function') {
	      $gamePlayer.initMover();
	      if (DEBUG_MODE) console.log('✈️ Player mover initialized');
	    }

	    if ($gamePlayer && $gamePlayer._followers) {
	      $gamePlayer._followers._data.forEach(function(follower) {
	        if (follower && typeof follower.initMover === 'function') {
	          follower.initMover();
	        }
	      });
	      if (DEBUG_MODE) console.log('✈️ Follower movers initialized');
	    }

	    // Initialize map character movers if the function exists
	    if ($gameMap && typeof $gameMap.initCharacterMovers === 'function') {
	      $gameMap.initCharacterMovers();
	      if (DEBUG_MODE) console.log('✈️ Map character movers initialized');
	    }
	  };

	  script.onerror = function() {
	    console.error('❌ Failed to load SAN_AnalogMove.js');
	  };

	  document.head.appendChild(script);
	  _sanAnalogMoveScript = script;

	  if (DEBUG_MODE) console.log('🔄 Loading SAN_AnalogMove...');
	}

	// Function to dynamically unload SAN_AnalogMovement
	function unloadSANAnalogMovement() {
	  if (!_sanAnalogMoveLoaded) {
	    if (DEBUG_MODE) console.log('🛬 SAN_AnalogMove already unloaded');
	    return;
	  }

	  // Disable the Sanshiro.AnalogMove system
	  if (typeof Sanshiro !== 'undefined' && Sanshiro.AnalogMove) {
	    Sanshiro.AnalogMove.enabled = false;
	    if (DEBUG_MODE) console.log('🛬 SAN_AnalogMove disabled');
	  }

	  // Remove all analog movement related functions and objects
	  if (typeof Game_Player !== 'undefined') {
	    delete Game_Player.prototype.canAnalogMove;
	    delete Game_Player.prototype.hasMover;
	    delete Game_Player.prototype.mover;
	  }

	  if (typeof Game_Follower !== 'undefined') {
	    delete Game_Follower.prototype.canAnalogMove;
	    delete Game_Follower.prototype.hasMover;
	    delete Game_Follower.prototype.mover;
	  }

	  if (typeof Game_CharacterBase !== 'undefined') {
	    delete Game_CharacterBase.prototype.canAnalogMove;
	    delete Game_CharacterBase.prototype.hasMover;
	    delete Game_CharacterBase.prototype.mover;
	  }

	  // Clear the Sanshiro namespace
	  if (typeof Sanshiro !== 'undefined') {
	    delete window.Sanshiro;
	  }

	  // Remove the script element
	  if (_sanAnalogMoveScript && _sanAnalogMoveScript.parentNode) {
	    _sanAnalogMoveScript.parentNode.removeChild(_sanAnalogMoveScript);
	    _sanAnalogMoveScript = null;
	  }

	  // Disable the plugin in the plugins list
	  const plugins = $plugins || [];
	  const sanPlugin = plugins.find(plugin => plugin.name === 'SAN_AnalogMove');
	  if (sanPlugin) {
	    sanPlugin.status = false;
	  }

	  _sanAnalogMoveLoaded = false;

	  if (DEBUG_MODE) console.log('🛬 SAN_AnalogMove completely unloaded');
	}

  const ultraParams = PluginManager.parameters('UltraMode7');
	
  const mauiPitch = PluginManager.parameters('MAUI_FF6Airship');
  
  // Configuration system for easy customization
  const AIRSHIP_CONFIG = {
    // UltraMode7 overrides
    ULTRA_MODE7: {
      CHARACTERS_ADJUST_SPRITE_DIRECTION: false,
      FADE_Z_END: 1200,
      DEFAULT_FAR_CLIP_Z: 1300,
      DEFAULT_PARALLAX_DISTANCE: 2000
    },
    
    // Camera settings
    CAMERA: {
      DEFAULT_Y: UltraMode7.DEFAULT_CAMERA_Y,
      TARGET_Y: 144,
      DEFAULT_PITCH: parseFloat(ultraParams.DEFAULT_PITCH),
      TARGET_PITCH: parseFloat(55)
    },
    
    // Altitude limits
    ALTITUDE: {
      MIN: 48,
      MAX: 200
    },
    
    // Effect thresholds - INCREASED for more dynamic range
    EFFECTS: {
      TRAIL_LOW_ALTITUDE: 120, // Increased from 60 to 120
      RIPPLE_LOW_ALTITUDE: 100, // Increased from 60 to 100
      DUST_CLOUD_THRESHOLD: 80, // Increased from 50 to 80
      CLOUD_FLYING_ALTITUDE: 150,
      WIND_LINES_ALTITUDE: 120
    },
    
    // Performance settings - OPTIMIZED for better balance
    PERFORMANCE: {
      UPDATE_FREQUENCY: 3, // Increased from 2 to 3 for better performance
      MAX_SPRITES_PER_TYPE: 50, // Reduced from 60 to 50 for better performance
      EMERGENCY_CLEANUP_THRESHOLD: 500 // Reduced from 600 to 500
    }
  };
  
  // Apply UltraMode7 configuration
  UltraMode7.CHARACTERS_ADJUST_SPRITE_DIRECTION = AIRSHIP_CONFIG.ULTRA_MODE7.CHARACTERS_ADJUST_SPRITE_DIRECTION;
  UltraMode7.FADE_Z_END = AIRSHIP_CONFIG.ULTRA_MODE7.FADE_Z_END;
  UltraMode7.DEFAULT_FAR_CLIP_Z = AIRSHIP_CONFIG.ULTRA_MODE7.DEFAULT_FAR_CLIP_Z;
  UltraMode7.DEFAULT_PARALLAX_DISTANCE = AIRSHIP_CONFIG.ULTRA_MODE7.DEFAULT_PARALLAX_DISTANCE;
  
  const defaultCameraY = AIRSHIP_CONFIG.CAMERA.DEFAULT_Y;
  const targetCameraY = AIRSHIP_CONFIG.CAMERA.TARGET_Y;
  const targetPitch = AIRSHIP_CONFIG.CAMERA.TARGET_PITCH;
  const defaultPitch = AIRSHIP_CONFIG.CAMERA.DEFAULT_PITCH;
  
  // Dynamic camera configuration
  const CAMERA_CONFIG = {
    // Pitch adjustment based on altitude
    PITCH: {
      MIN: 35, // Pitch when at minimum altitude
      MAX: 80  // Pitch when at maximum altitude
    },
    
    // FOV adjustment based on altitude
    FOV: {
      MIN: 45, // Narrow FOV when low (more zoomed in)
      MAX: 65  // Wide FOV when high (more zoomed out)
    },
    
    // Camera distance adjustment based on altitude
    DISTANCE: {
      MIN: 600, // Further camera when low (see more of world)
      MAX: 350  // Closer camera when high (more focused view)
    },
    
    // Far clip distance adjustment based on altitude
    FAR_CLIP: {
      MIN: 2000, // Much longer view distance when low
      MAX: 3000  // Maximum view distance when high
    },
    
      // Banking effect
  BANKING: {
    MAX_ROLL: 2 // Maximum 2 degrees of roll
  },
  
      // Airship sprite banking/tilting
    AIRSHIP_BANKING: {
      ENABLED: true,
      MAX_TILT: 25, // Maximum tilt in degrees
      TILT_SPEED: 0.1, // How fast the airship tilts
      RECOVERY_SPEED: 0.05, // How fast it returns to level
      // Tilt based on movement direction
      DIRECTION_TILT: {
        ENABLED: true,
        FORWARD_TILT: 5, // Tilt forward when moving up
        BACKWARD_TILT: -3, // Tilt backward when moving down
        SIDE_TILT: 15 // Tilt sideways when turning
      },
      
      // Advanced banking effects
      ADVANCED: {
        ENABLED: true,
        // G-force simulation
        G_FORCE: {
          ENABLED: true,
          MAX_G_FORCE: 3.0,
          COMPRESSION_FACTOR: 0.8
        },
        
        // Aerodynamic effects
        AERODYNAMIC: {
          ENABLED: true,
          WIND_RESISTANCE: 0.1,
          TURBULENCE: 0.05
        },
        
        // Dramatic banking for sharp turns
        DRAMATIC_TURNS: {
          ENABLED: true,
          SHARP_TURN_THRESHOLD: 45, // Degrees
          DRAMATIC_TILT_MULTIPLIER: 2.0
        },
        
              // Directional drift toward screen edges
      DIRECTIONAL_DRIFT: {
        ENABLED: true,
        DRIFT_SPEED: 0.02, // How fast it drifts toward edges
        MAX_DRIFT_DISTANCE: 100, // Maximum pixels from center
        RECOVERY_SPEED: 0.01, // How fast it returns to center
        SCREEN_EDGE_THRESHOLD: 0.3, // When to start drifting (30% from center)
        MOMENTUM_DRIFT: {
          ENABLED: true,
          MOMENTUM_DECAY: 0.95, // How quickly momentum fades
          MOMENTUM_MULTIPLIER: 1.5 // How much momentum affects drift
        }
      }
      }
    },
    
    // NEW: Advanced camera effects
    ADVANCED_EFFECTS: {
      // Cinematic camera shake based on speed/altitude
      SHAKE: {
        ENABLED: true,
        INTENSITY_MULTIPLIER: 0.3,
        FREQUENCY: 0.1
      },
      
      // Dynamic depth of field (blur background at high altitude)
      DEPTH_OF_FIELD: {
        ENABLED: true,
        MAX_BLUR: 0.8,
        FOCUS_DISTANCE: 1000
      },
      
      // Camera zoom effects
      ZOOM: {
        ENABLED: true,
        MIN_ZOOM: 0.6, // More extreme zoom out (was 0.8)
        MAX_ZOOM: 2.5, // Much more extreme zoom in (was 1.2)
        ZOOM_SPEED: 0.03, // Slightly faster zoom transitions (was 0.02)
        // NEW: Extreme altitude zoom settings
        EXTREME_ALTITUDE: {
          ENABLED: true,
          TRIGGER_ALTITUDE: 0.8, // Trigger at 80% of max altitude
          MAX_ZOOM: 4.0, // Extreme zoom at very high altitude
          ZOOM_CURVE: 2.5 // Exponential curve for more dramatic effect
        }
      },
      
      // Motion blur for high-speed movement
      MOTION_BLUR: {
        ENABLED: true,
        INTENSITY: 0.4,
        THRESHOLD_SPEED: 5.0
      },
      
      // Cinematic letterboxing at dramatic moments
      LETTERBOX: {
        ENABLED: true,
        HEIGHT: 0.1, // 10% of screen height
        TRANSITION_SPEED: 0.05
      },
      
      // Cinematic camera movements
      CINEMATIC: {
        ENABLED: true,
        ORBIT_SPEED: 0.02,
        ORBIT_RADIUS: 50,
        DRAMATIC_ANGLE_CHANGES: true,
        SLOW_MOTION_THRESHOLD: 0.3
      },
      
      // Dolly zoom effect (Hitchcock effect)
      DOLLY_ZOOM: {
        ENABLED: true,
        TRIGGER_ALTITUDE: 160, // Lower trigger altitude for earlier effect (was 180)
        ZOOM_SPEED: 0.04, // Faster dolly zoom (was 0.03)
        MAX_ZOOM: 3.5, // Much more extreme dolly zoom (was 2.0)
        // NEW: Extreme dolly zoom at maximum altitude
        EXTREME_DOLLY: {
          ENABLED: true,
          TRIGGER_ALTITUDE: 0.95, // Trigger at 95% of max altitude
          MAX_ZOOM: 5.0, // Extreme dolly zoom at maximum altitude
          ZOOM_CURVE: 3.0 // Very dramatic exponential curve
        }
      },
      
      // Advanced cinematic effects
      ADVANCED_CINEMATIC: {
        ENABLED: true,
        // Dutch angle effect (tilted camera)
        DUTCH_ANGLE: {
          ENABLED: true,
          MAX_TILT: 25, // Maximum tilt in degrees
          TRIGGER_SPEED: 2.0
        },
        
        // Camera whip pan (quick direction changes)
        WHIP_PAN: {
          ENABLED: true,
          THRESHOLD_ANGLE_CHANGE: 45, // Degrees
          PAN_SPEED: 0.15
        },
        
        // Breathing camera (subtle movement)
        BREATHING: {
          ENABLED: true,
          AMPLITUDE: 2.0,
          FREQUENCY: 0.05
        },
        
        // Dramatic slow motion
        SLOW_MOTION: {
          ENABLED: true,
          TRIGGER_SPEED: 4.0,
          SLOW_FACTOR: 0.3
        },
        
        // Camera roll effects
        ROLL: {
          ENABLED: true,
          MAX_ROLL: 15,
          ROLL_SPEED: 0.02
        },
        
        // Impossible camera effects
        IMPOSSIBLE: {
          ENABLED: true,
          // Matrix-style bullet time
          BULLET_TIME: {
            ENABLED: true,
            TRIGGER_SPEED: 5.0,
            TIME_SCALE: 0.1
          },
          
          // Camera teleportation
          TELEPORT: {
            ENABLED: true,
            TRIGGER_ALTITUDE: 190,
            TELEPORT_DISTANCE: 100
          },
          
                  // Gravity-defying camera
        GRAVITY_DEFY: {
          ENABLED: true,
          INVERT_Y: true,
          FLIP_HORIZONTAL: false
        },
        
        // Camera personality system
        PERSONALITY: {
          ENABLED: true,
          // Camera gets excited at high speeds
          EXCITEMENT: {
            ENABLED: true,
            TRIGGER_SPEED: 3.0,
            EXCITEMENT_MULTIPLIER: 1.5
          },
          
          // Camera gets nervous at high altitude
          NERVOUSNESS: {
            ENABLED: true,
            TRIGGER_ALTITUDE: 150,
            SHAKE_MULTIPLIER: 2.0
          },
          
          // Camera gets curious about direction changes
          CURIOSITY: {
            ENABLED: true,
            LOOK_AHEAD_DISTANCE: 50,
            CURIOUS_ANGLE: 30
          }
        }
        }
      }
    }
  };
  
  let lastYawDirection = 0; // Track last yaw direction for roll calculation
  
  var _parallaxRotation = 0;
  
  let wasFlying = false;
  let pitchSetActive = false;
  let pitchResetActive = false;
  
      // Airship banking/tilting system
    const AirshipBanking = {
      currentTilt: { x: 0, y: 0, z: 0 }, // X=roll, Y=pitch, Z=yaw
      targetTilt: { x: 0, y: 0, z: 0 },
      lastDirection: 0,
      lastAltitude: 0,
      
      // Advanced banking variables
      gForceEffect: 0,
      turbulenceOffset: { x: 0, y: 0 },
      dramaticTurnActive: false,
      
      // Directional drift variables
      driftOffset: { x: 0, y: 0 },
      targetDrift: { x: 0, y: 0 },
      screenCenter: { x: 0, y: 0 },
      momentumDrift: { x: 0, y: 0 },
      lastDirection: 0,
    
    // Initialize banking
    init: function() {
      this.currentTilt = { x: 0, y: 0, z: 0 };
      this.targetTilt = { x: 0, y: 0, z: 0 };
      this.lastDirection = 0;
      this.lastAltitude = 0;
      this.gForceEffect = 0;
      this.turbulenceOffset = { x: 0, y: 0 };
      this.dramaticTurnActive = false;
      this.driftOffset = { x: 0, y: 0 };
      this.targetDrift = { x: 0, y: 0 };
      this.screenCenter = { x: Graphics.width / 2, y: Graphics.height / 2 };
      this.momentumDrift = { x: 0, y: 0 };
      this.lastDirection = 0;
    },
    
    // Update airship tilt based on movement
    updateTilt: function(airship) {
      if (!CAMERA_CONFIG.AIRSHIP_BANKING.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const direction = Math.atan2(airship._velocityY, airship._velocityX);
      const altitude = airship._altitude || 0;
      
      // Calculate direction change for banking
      const directionChange = direction - this.lastDirection;
      const normalizedDirectionChange = Math.atan2(Math.sin(directionChange), Math.cos(directionChange));
      
      // Set target tilt based on movement
      this.targetTilt.x = 0; // Roll (sideways tilt)
      this.targetTilt.y = 0; // Pitch (forward/backward tilt)
      this.targetTilt.z = 0; // Yaw (rotation)
      
      // Banking based on direction changes
      if (Math.abs(normalizedDirectionChange) > 0.1) {
        this.targetTilt.x = normalizedDirectionChange * CAMERA_CONFIG.AIRSHIP_BANKING.DIRECTION_TILT.SIDE_TILT;
      }
      
      // Forward/backward tilt based on vertical movement
      if (airship._velocityY < -0.5) {
        // Moving up - tilt forward
        this.targetTilt.y = CAMERA_CONFIG.AIRSHIP_BANKING.DIRECTION_TILT.FORWARD_TILT;
      } else if (airship._velocityY > 0.5) {
        // Moving down - tilt backward
        this.targetTilt.y = CAMERA_CONFIG.AIRSHIP_BANKING.DIRECTION_TILT.BACKWARD_TILT;
      }
      
      // Altitude-based tilt (higher altitude = more dramatic)
      const altitudeFactor = Math.min(altitude / AIRSHIP_CONFIG.ALTITUDE.MAX, 1.0);
      this.targetTilt.x *= (1 + altitudeFactor * 0.5);
      this.targetTilt.y *= (1 + altitudeFactor * 0.3);
      
      // Advanced banking effects
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.ENABLED) {
        this.updateAdvancedBanking(airship, speed, direction, altitude);
      }
      
      // Clamp to maximum tilt
      const maxTilt = CAMERA_CONFIG.AIRSHIP_BANKING.MAX_TILT;
      this.targetTilt.x = Math.max(-maxTilt, Math.min(maxTilt, this.targetTilt.x));
      this.targetTilt.y = Math.max(-maxTilt, Math.min(maxTilt, this.targetTilt.y));
      
      // Smoothly interpolate current tilt to target
      const tiltSpeed = CAMERA_CONFIG.AIRSHIP_BANKING.TILT_SPEED;
      const recoverySpeed = CAMERA_CONFIG.AIRSHIP_BANKING.RECOVERY_SPEED;
      
      if (speed > 0.5) {
        // Moving - apply tilt
        this.currentTilt.x += (this.targetTilt.x - this.currentTilt.x) * tiltSpeed;
        this.currentTilt.y += (this.targetTilt.y - this.currentTilt.y) * tiltSpeed;
      } else {
        // Not moving - recover to level
        this.currentTilt.x *= (1 - recoverySpeed);
        this.currentTilt.y *= (1 - recoverySpeed);
      }
      
      // Apply tilt to airship sprite
      this.applyTiltToSprite(airship);
      
      this.lastDirection = direction;
      this.lastAltitude = altitude;
    },
    
    // Update advanced banking effects
    updateAdvancedBanking: function(airship, speed, direction, altitude) {
      // G-force simulation
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.G_FORCE.ENABLED) {
        this.updateGForce(speed, direction);
      }
      
      // Aerodynamic effects
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.AERODYNAMIC.ENABLED) {
        this.updateAerodynamics(speed, altitude);
      }
      
      // Dramatic turns
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DRAMATIC_TURNS.ENABLED) {
        this.updateDramaticTurns(direction);
      }
      
      // Directional drift
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.ENABLED) {
        this.updateDirectionalDrift(airship, speed, direction);
      }
    },
    
    // Update G-force effects
    updateGForce: function(speed, direction) {
      const maxGForce = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.G_FORCE.MAX_G_FORCE;
      const compressionFactor = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.G_FORCE.COMPRESSION_FACTOR;
      
      // Calculate G-force based on speed and direction changes
      this.gForceEffect = Math.min(maxGForce, speed * 0.5);
      
      // Apply compression effect (squash the sprite)
      if (this.gForceEffect > 0.5) {
        const compression = 1.0 - (this.gForceEffect * compressionFactor);
        if (airship && airship.sprite) {
          airship.sprite.scale.y = compression;
        }
      }
    },
    
    // Update aerodynamic effects
    updateAerodynamics: function(speed, altitude) {
      const windResistance = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.AERODYNAMIC.WIND_RESISTANCE;
      const turbulence = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.AERODYNAMIC.TURBULENCE;
      
      // Wind resistance effect
      if (speed > 2.0) {
        const resistance = speed * windResistance;
        this.targetTilt.x += Math.sin(Date.now() * 0.01) * resistance;
      }
      
      // Turbulence at high altitude
      if (altitude > AIRSHIP_CONFIG.ALTITUDE.MAX * 0.7) {
        this.turbulenceOffset.x = Math.sin(Date.now() * 0.02) * turbulence;
        this.turbulenceOffset.y = Math.cos(Date.now() * 0.015) * turbulence;
      } else {
        this.turbulenceOffset.x *= 0.95;
        this.turbulenceOffset.y *= 0.95;
      }
    },
    
    // Update dramatic turns
    updateDramaticTurns: function(direction) {
      const threshold = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DRAMATIC_TURNS.SHARP_TURN_THRESHOLD * (Math.PI / 180);
      const multiplier = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DRAMATIC_TURNS.DRAMATIC_TILT_MULTIPLIER;
      
      const directionChange = Math.abs(direction - this.lastDirection);
      
      if (directionChange > threshold) {
        this.dramaticTurnActive = true;
        this.targetTilt.x *= multiplier;
        this.targetTilt.y *= multiplier;
      } else {
        this.dramaticTurnActive = false;
      }
    },
    
    // Update directional drift toward screen edges
    updateDirectionalDrift: function(airship, speed, direction) {
      const driftConfig = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT;
      const driftSpeed = driftConfig.DRIFT_SPEED;
      const maxDriftDistance = driftConfig.MAX_DRIFT_DISTANCE;
      const recoverySpeed = driftConfig.RECOVERY_SPEED;
      const edgeThreshold = driftConfig.SCREEN_EDGE_THRESHOLD;
      
      // Calculate current position relative to screen center
      const currentX = airship.x;
      const currentY = airship.y;
      const screenWidth = Graphics.width;
      const screenHeight = Graphics.height;
      
      // Calculate how far from center the airship is (0 = center, 1 = edge)
      const distanceFromCenterX = Math.abs(currentX - this.screenCenter.x) / (screenWidth / 2);
      const distanceFromCenterY = Math.abs(currentY - this.screenCenter.y) / (screenHeight / 2);
      
      // Determine target drift based on movement direction
      this.targetDrift.x = 0;
      this.targetDrift.y = 0;
      
      if (speed > 0.5) { // Only drift when moving
              // Calculate direction components
      const dirX = Math.cos(direction);
      const dirY = Math.sin(direction);
      
      // Update momentum based on direction changes
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.MOMENTUM_DRIFT.ENABLED) {
        const directionChange = Math.abs(direction - this.lastDirection);
        if (directionChange > 0.1) { // Significant direction change
          // Add momentum in the old direction
          const oldDirX = Math.cos(this.lastDirection);
          const oldDirY = Math.sin(this.lastDirection);
          const momentumMultiplier = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.MOMENTUM_DRIFT.MOMENTUM_MULTIPLIER;
          
          this.momentumDrift.x += oldDirX * maxDriftDistance * momentumMultiplier;
          this.momentumDrift.y += oldDirY * maxDriftDistance * momentumMultiplier;
        }
        
        // Decay momentum
        const momentumDecay = CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.DIRECTIONAL_DRIFT.MOMENTUM_DRIFT.MOMENTUM_DECAY;
        this.momentumDrift.x *= momentumDecay;
        this.momentumDrift.y *= momentumDecay;
      }
      
      // Determine which screen edge to drift toward
      if (dirX > 0.3) { // Moving right
        this.targetDrift.x = maxDriftDistance;
      } else if (dirX < -0.3) { // Moving left
        this.targetDrift.x = -maxDriftDistance;
      }
      
      if (dirY > 0.3) { // Moving down
        this.targetDrift.y = maxDriftDistance;
      } else if (dirY < -0.3) { // Moving up
        this.targetDrift.y = -maxDriftDistance;
      }
        
        // Apply speed multiplier for more dramatic effect at higher speeds
        const speedMultiplier = Math.min(speed / 3.0, 2.0);
        this.targetDrift.x *= speedMultiplier;
        this.targetDrift.y *= speedMultiplier;
      }
      
      // Smoothly interpolate current drift to target
      if (speed > 0.5) {
        // Moving - drift toward target (including momentum)
        const totalTargetX = this.targetDrift.x + this.momentumDrift.x;
        const totalTargetY = this.targetDrift.y + this.momentumDrift.y;
        
        this.driftOffset.x += (totalTargetX - this.driftOffset.x) * driftSpeed;
        this.driftOffset.y += (totalTargetY - this.driftOffset.y) * driftSpeed;
      } else {
        // Not moving - return to center
        this.driftOffset.x *= (1 - recoverySpeed);
        this.driftOffset.y *= (1 - recoverySpeed);
      }
      
      // Clamp drift to maximum distance
      this.driftOffset.x = Math.max(-maxDriftDistance, Math.min(maxDriftDistance, this.driftOffset.x));
      this.driftOffset.y = Math.max(-maxDriftDistance, Math.min(maxDriftDistance, this.driftOffset.y));
      
      // Update last direction for momentum calculations
      this.lastDirection = direction;
    },
    
    // Apply tilt to the airship sprite
    applyTiltToSprite: function(airship) {
      if (!airship || !airship.sprite) return;
      
      const sprite = airship.sprite;
      
      // Convert degrees to radians and apply rotation
      const rollRad = this.currentTilt.x * (Math.PI / 180);
      const pitchRad = this.currentTilt.y * (Math.PI / 180);
      
      // Apply 3D rotation to sprite (simplified 2D approximation)
      sprite.rotation = rollRad; // Roll affects Z rotation
      
      // Apply scale changes to simulate pitch (forward/backward tilt)
      const pitchScale = 1.0 + Math.sin(pitchRad) * 0.1;
      sprite.scale.y = pitchScale;
      
      // Add slight position offset for more realistic effect
      const offsetY = Math.sin(pitchRad) * 5;
      sprite.y += offsetY;
      
      // Apply advanced effects
      if (CAMERA_CONFIG.AIRSHIP_BANKING.ADVANCED.ENABLED) {
        // Apply turbulence offset
        sprite.x += this.turbulenceOffset.x;
        sprite.y += this.turbulenceOffset.y;
        
        // Apply directional drift offset
        sprite.x += this.driftOffset.x;
        sprite.y += this.driftOffset.y;
        
        // Apply G-force compression (if not already applied)
        if (this.gForceEffect > 0.5 && !this.dramaticTurnActive) {
          const compression = 1.0 - (this.gForceEffect * 0.1);
          sprite.scale.y *= compression;
        }
        
        // Dramatic turn effects
        if (this.dramaticTurnActive) {
          sprite.scale.x = 1.2; // Stretch horizontally during sharp turns
          sprite.alpha = 0.9; // Slight transparency for dramatic effect
        } else {
          sprite.scale.x = 1.0;
          sprite.alpha = 1.0;
        }
      }
    },
    
    // Reset all tilts
    reset: function() {
      this.init();
    }
  };
  
  // NEW: Advanced camera effects system
  const AdvancedCameraEffects = {
    // Camera shake variables
    shakeOffset: { x: 0, y: 0 },
    shakeTimer: 0,
    shakeIntensity: 0,
    
    // Depth of field variables
    dofBlur: 0,
    dofTarget: 0,
    
    // Zoom variables
    currentZoom: 1.0,
    targetZoom: 1.0,
    
    // Motion blur variables
    motionBlurIntensity: 0,
    lastPosition: { x: 0, y: 0 },
    
    // Letterbox variables
    letterboxHeight: 0,
    letterboxTarget: 0,
    
    // Cinematic camera variables
    orbitAngle: 0,
    orbitRadius: 0,
    dramaticAngle: 0,
    slowMotionFactor: 1.0,
    
    // Dolly zoom variables
    dollyZoomActive: false,
    dollyZoomIntensity: 0,
    
    // Advanced cinematic variables
    dutchAngle: 0,
    whipPanActive: false,
    whipPanAngle: 0,
    breathingOffset: { x: 0, y: 0 },
    breathingTimer: 0,
    slowMotionActive: false,
    cameraRoll: 0,
    lastDirection: 0,
    
    // Impossible camera variables
    bulletTimeActive: false,
    teleportActive: false,
    teleportTimer: 0,
    gravityDefyActive: false,
    
    // Initialize effects
    init: function() {
      this.shakeOffset = { x: 0, y: 0 };
      this.shakeTimer = 0;
      this.shakeIntensity = 0;
      this.dofBlur = 0;
      this.dofTarget = 0;
      this.currentZoom = 1.0;
      this.targetZoom = 1.0;
      this.motionBlurIntensity = 0;
      this.lastPosition = { x: 0, y: 0 };
      this.letterboxHeight = 0;
      this.letterboxTarget = 0;
      this.orbitAngle = 0;
      this.orbitRadius = 0;
      this.dramaticAngle = 0;
      this.slowMotionFactor = 1.0;
      this.dollyZoomActive = false;
      this.dollyZoomIntensity = 0;
      this.dutchAngle = 0;
      this.whipPanActive = false;
      this.whipPanAngle = 0;
      this.breathingOffset = { x: 0, y: 0 };
      this.breathingTimer = 0;
      this.slowMotionActive = false;
      this.cameraRoll = 0;
      this.lastDirection = 0;
      this.bulletTimeActive = false;
      this.teleportActive = false;
      this.teleportTimer = 0;
      this.gravityDefyActive = false;
    },
    
    // Update camera shake based on airship movement
    updateShake: function(airship, altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.SHAKE.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const altitudeFactor = Math.min(altitude / AIRSHIP_CONFIG.ALTITUDE.MAX, 1.0);
      
      // Calculate shake intensity based on speed and altitude
      this.shakeIntensity = speed * altitudeFactor * CAMERA_CONFIG.ADVANCED_EFFECTS.SHAKE.INTENSITY_MULTIPLIER;
      
      if (this.shakeIntensity > 0.1) {
        this.shakeTimer += CAMERA_CONFIG.ADVANCED_EFFECTS.SHAKE.FREQUENCY;
        this.shakeOffset.x = Math.sin(this.shakeTimer) * this.shakeIntensity;
        this.shakeOffset.y = Math.cos(this.shakeTimer * 1.3) * this.shakeIntensity;
      } else {
        this.shakeOffset.x *= 0.9;
        this.shakeOffset.y *= 0.9;
      }
    },
    
    // Update depth of field effect
    updateDepthOfField: function(altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.DEPTH_OF_FIELD.ENABLED) return;
      
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      this.dofTarget = altitudeRatio * CAMERA_CONFIG.ADVANCED_EFFECTS.DEPTH_OF_FIELD.MAX_BLUR;
      this.dofBlur += (this.dofTarget - this.dofBlur) * 0.05;
    },
    
    // Update camera zoom based on dramatic moments
    updateZoom: function(airship, altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      
      // Enhanced zoom calculation with extreme altitude effects
      let zoomFactor = 1.0;
      
      if (speed > 3.0) {
        // Zoom out at high speeds
        zoomFactor = 1.0 - (speed - 3.0) * 0.15; // More dramatic speed zoom (was 0.1)
      } else if (altitudeRatio > 0.7) {
        // Enhanced altitude-based zoom with extreme settings
        if (CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.EXTREME_ALTITUDE.ENABLED && 
            altitudeRatio >= CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.EXTREME_ALTITUDE.TRIGGER_ALTITUDE) {
          // Extreme altitude zoom with exponential curve
          const extremeRatio = (altitudeRatio - CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.EXTREME_ALTITUDE.TRIGGER_ALTITUDE) / 
                              (1.0 - CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.EXTREME_ALTITUDE.TRIGGER_ALTITUDE);
          const curve = CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.EXTREME_ALTITUDE.ZOOM_CURVE;
          const extremeZoom = Math.pow(extremeRatio, curve) * 
                             (CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.EXTREME_ALTITUDE.MAX_ZOOM - 1.0);
          zoomFactor = 1.0 + extremeZoom;
          
          // Debug logging for extreme zoom
          if (DEBUG_MODE && altitudeRatio > 0.95) {
            console.log(`🔍 Extreme Zoom: Altitude ${altitude}/${AIRSHIP_CONFIG.ALTITUDE.MAX} (${(altitudeRatio*100).toFixed(1)}%), Zoom: ${zoomFactor.toFixed(2)}x`);
          }
        } else {
          // Regular altitude zoom (enhanced)
          zoomFactor = 1.0 + (altitudeRatio * 0.4); // More dramatic regular zoom (was 0.2)
        }
      }
      
      // Apply zoom limits
      this.targetZoom = Math.max(CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.MIN_ZOOM, 
                                Math.min(CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.MAX_ZOOM, zoomFactor));
      this.currentZoom += (this.targetZoom - this.currentZoom) * CAMERA_CONFIG.ADVANCED_EFFECTS.ZOOM.ZOOM_SPEED;
    },
    
    // Update motion blur effect
    updateMotionBlur: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.MOTION_BLUR.ENABLED) return;
      
      const currentPos = { x: airship.x, y: airship.y };
      const speed = Math.sqrt((currentPos.x - this.lastPosition.x) ** 2 + (currentPos.y - this.lastPosition.y) ** 2);
      
      if (speed > CAMERA_CONFIG.ADVANCED_EFFECTS.MOTION_BLUR.THRESHOLD_SPEED) {
        this.motionBlurIntensity = Math.min(1.0, speed * CAMERA_CONFIG.ADVANCED_EFFECTS.MOTION_BLUR.INTENSITY);
      } else {
        this.motionBlurIntensity *= 0.95;
      }
      
      this.lastPosition = currentPos;
    },
    
    // Update cinematic letterboxing
    updateLetterbox: function(airship, altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.LETTERBOX.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      
      // Add letterboxing during dramatic moments
      let letterboxTarget = 0;
      if (speed > 4.0 || altitudeRatio > 0.8) {
        letterboxTarget = CAMERA_CONFIG.ADVANCED_EFFECTS.LETTERBOX.HEIGHT;
      }
      
      this.letterboxTarget = letterboxTarget;
      this.letterboxHeight += (this.letterboxTarget - this.letterboxHeight) * CAMERA_CONFIG.ADVANCED_EFFECTS.LETTERBOX.TRANSITION_SPEED;
    },
    
    // Update cinematic camera movements
    updateCinematic: function(airship, altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      
      // Orbital camera movement around airship
      this.orbitAngle += CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.ORBIT_SPEED;
      this.orbitRadius = CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.ORBIT_RADIUS * (1 + altitudeRatio * 0.5);
      
      // Dramatic angle changes during high-speed moments
      if (CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.DRAMATIC_ANGLE_CHANGES) {
        if (speed > 3.0) {
          this.dramaticAngle = Math.sin(this.orbitAngle * 2) * 15; // 15-degree dramatic tilt
        } else {
          this.dramaticAngle *= 0.95; // Smoothly return to normal
        }
      }
      
      // Slow motion effect during dramatic moments
      if (speed > CAMERA_CONFIG.ADVANCED_EFFECTS.CINEMATIC.SLOW_MOTION_THRESHOLD) {
        this.slowMotionFactor = 0.7; // Slow down time slightly
      } else {
        this.slowMotionFactor = 1.0;
      }
    },
    
    // Update dolly zoom effect (Hitchcock effect)
    updateDollyZoom: function(altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.ENABLED) return;
      
      const triggerAltitude = CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.TRIGGER_ALTITUDE;
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      
      if (altitude >= triggerAltitude) {
        this.dollyZoomActive = true;
        
        // Enhanced dolly zoom calculation with extreme settings
        if (CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.ENABLED && 
            altitudeRatio >= CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.TRIGGER_ALTITUDE) {
          // Extreme dolly zoom at maximum altitude
          const extremeRatio = (altitudeRatio - CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.TRIGGER_ALTITUDE) / 
                              (1.0 - CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.TRIGGER_ALTITUDE);
          const curve = CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.ZOOM_CURVE;
          this.dollyZoomIntensity = Math.min(1.0, Math.pow(extremeRatio, curve));
          
          // Debug logging for extreme dolly zoom
          if (DEBUG_MODE && altitudeRatio > 0.98) {
            console.log(`🎬 Extreme Dolly Zoom: Altitude ${altitude}/${AIRSHIP_CONFIG.ALTITUDE.MAX} (${(altitudeRatio*100).toFixed(1)}%), Intensity: ${this.dollyZoomIntensity.toFixed(2)}`);
          }
        } else {
          // Regular dolly zoom (enhanced)
          const intensity = (altitude - triggerAltitude) / (AIRSHIP_CONFIG.ALTITUDE.MAX - triggerAltitude);
          this.dollyZoomIntensity = Math.min(1.0, intensity * 1.5); // More dramatic regular dolly zoom
        }
      } else {
        this.dollyZoomActive = false;
        this.dollyZoomIntensity *= 0.95; // Fade out
      }
    },
    
    // Update Dutch angle effect (tilted camera)
    updateDutchAngle: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.DUTCH_ANGLE.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const triggerSpeed = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.DUTCH_ANGLE.TRIGGER_SPEED;
      const maxTilt = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.DUTCH_ANGLE.MAX_TILT;
      
      if (speed > triggerSpeed) {
        // Calculate direction-based tilt
        const direction = Math.atan2(airship._velocityY, airship._velocityX);
        this.dutchAngle = Math.sin(direction) * maxTilt;
      } else {
        this.dutchAngle *= 0.95; // Smooth return to normal
      }
    },
    
    // Update whip pan effect (quick direction changes)
    updateWhipPan: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.WHIP_PAN.ENABLED) return;
      
      const currentDirection = Math.atan2(airship._velocityY, airship._velocityX);
      const angleChange = Math.abs(currentDirection - this.lastDirection);
      const threshold = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.WHIP_PAN.THRESHOLD_ANGLE_CHANGE * (Math.PI / 180);
      
      if (angleChange > threshold) {
        this.whipPanActive = true;
        this.whipPanAngle = currentDirection;
      } else {
        this.whipPanActive = false;
      }
      
      this.lastDirection = currentDirection;
    },
    
    // Update breathing camera effect
    updateBreathing: function() {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.BREATHING.ENABLED) return;
      
      this.breathingTimer += CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.BREATHING.FREQUENCY;
      const amplitude = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.BREATHING.AMPLITUDE;
      
      this.breathingOffset.x = Math.sin(this.breathingTimer) * amplitude;
      this.breathingOffset.y = Math.cos(this.breathingTimer * 1.3) * amplitude;
    },
    
    // Update dramatic slow motion
    updateSlowMotion: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.SLOW_MOTION.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const triggerSpeed = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.SLOW_MOTION.TRIGGER_SPEED;
      const slowFactor = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.SLOW_MOTION.SLOW_FACTOR;
      
      if (speed > triggerSpeed) {
        this.slowMotionActive = true;
        this.slowMotionFactor = slowFactor;
      } else {
        this.slowMotionActive = false;
        this.slowMotionFactor = 1.0;
      }
    },
    
    // Update camera roll effects
    updateCameraRoll: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.ROLL.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const maxRoll = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.ROLL.MAX_ROLL;
      const rollSpeed = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.ROLL.ROLL_SPEED;
      
      if (speed > 2.0) {
        this.cameraRoll += rollSpeed;
        if (this.cameraRoll > maxRoll) this.cameraRoll = maxRoll;
      } else {
        this.cameraRoll *= 0.95;
      }
    },
    
    // Update Matrix-style bullet time effect
    updateBulletTime: function(airship) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.BULLET_TIME.ENABLED) return;
      
      const speed = Math.sqrt(airship._velocityX ** 2 + airship._velocityY ** 2);
      const triggerSpeed = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.BULLET_TIME.TRIGGER_SPEED;
      const timeScale = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.BULLET_TIME.TIME_SCALE;
      
      if (speed > triggerSpeed) {
        this.bulletTimeActive = true;
        this.slowMotionFactor = timeScale;
      } else {
        this.bulletTimeActive = false;
        this.slowMotionFactor = 1.0;
      }
    },
    
    // Update camera teleportation effect
    updateTeleport: function(altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.TELEPORT.ENABLED) return;
      
      const triggerAltitude = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.TELEPORT.TRIGGER_ALTITUDE;
      const teleportDistance = CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.TELEPORT.TELEPORT_DISTANCE;
      
      if (altitude >= triggerAltitude && !this.teleportActive) {
        this.teleportActive = true;
        this.teleportTimer = 30; // 30 frames of teleport effect
      }
      
      if (this.teleportActive && this.teleportTimer > 0) {
        this.teleportTimer--;
        // Create teleport effect by moving camera rapidly
        if (UltraMode7 && UltraMode7.camera) {
          const teleportOffset = Math.sin(this.teleportTimer * 0.5) * teleportDistance;
          UltraMode7.camera.x += teleportOffset;
          UltraMode7.camera.y += teleportOffset;
        }
      } else {
        this.teleportActive = false;
      }
    },
    
    // Update gravity-defying camera effect
    updateGravityDefy: function(altitude) {
      if (!CAMERA_CONFIG.ADVANCED_EFFECTS.ADVANCED_CINEMATIC.IMPOSSIBLE.GRAVITY_DEFY.ENABLED) return;
      
      const altitudeRatio = altitude / AIRSHIP_CONFIG.ALTITUDE.MAX;
      
      if (altitudeRatio > 0.9) { // At very high altitude
        this.gravityDefyActive = true;
        // Invert Y movement for gravity-defying effect
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.scale.y = -1; // Flip vertically
        }
      } else {
        this.gravityDefyActive = false;
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.scale.y = 1; // Return to normal
        }
      }
    },
    
    // Apply all camera effects
    applyEffects: function() {
      // Apply shake offset
      if (this.shakeOffset.x !== 0 || this.shakeOffset.y !== 0) {
        // Apply to UltraMode7 camera
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.x += this.shakeOffset.x;
          UltraMode7.camera.y += this.shakeOffset.y;
        }
      }
      
      // Apply zoom effect
      if (this.currentZoom !== 1.0) {
        // Apply zoom to camera scale
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.scale.set(this.currentZoom, this.currentZoom);
        }
      }
      
      // Apply cinematic orbital movement
      if (this.orbitRadius > 0) {
        if (UltraMode7 && UltraMode7.camera) {
          const orbitX = Math.cos(this.orbitAngle) * this.orbitRadius;
          const orbitY = Math.sin(this.orbitAngle) * this.orbitRadius;
          UltraMode7.camera.x += orbitX * 0.1;
          UltraMode7.camera.y += orbitY * 0.1;
        }
      }
      
      // Apply dramatic angle (rotation)
      if (this.dramaticAngle !== 0) {
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.rotation = this.dramaticAngle * (Math.PI / 180);
        }
      }
      
      // Apply slow motion effect
      if (this.slowMotionFactor !== 1.0) {
        // This would require frame rate manipulation
        // For now, we'll just track the value
      }
      
      // Apply dolly zoom effect
      if (this.dollyZoomActive && this.dollyZoomIntensity > 0) {
        if (UltraMode7 && UltraMode7.camera) {
          // Enhanced dolly zoom with extreme altitude support
          let dollyZoom = 1.0;
          
          // Check if we're in extreme dolly zoom territory
          const altitudeRatio = ($gameMap.airship() && $gameMap.airship()._altitude) ? 
                               $gameMap.airship()._altitude / AIRSHIP_CONFIG.ALTITUDE.MAX : 0;
          
          if (CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.ENABLED && 
              altitudeRatio >= CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.TRIGGER_ALTITUDE) {
            // Use extreme dolly zoom values
            dollyZoom = 1.0 + (this.dollyZoomIntensity * CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.EXTREME_DOLLY.MAX_ZOOM);
          } else {
            // Use regular dolly zoom values
            dollyZoom = 1.0 + (this.dollyZoomIntensity * CAMERA_CONFIG.ADVANCED_EFFECTS.DOLLY_ZOOM.MAX_ZOOM);
          }
          
          UltraMode7.camera.scale.set(dollyZoom, dollyZoom);
        }
      }
      
      // Apply Dutch angle effect
      if (this.dutchAngle !== 0) {
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.rotation = this.dutchAngle * (Math.PI / 180);
        }
      }
      
      // Apply whip pan effect
      if (this.whipPanActive) {
        if (UltraMode7 && UltraMode7.camera) {
          const panOffset = Math.sin(this.whipPanAngle) * 20;
          UltraMode7.camera.x += panOffset;
        }
      }
      
      // Apply breathing camera effect
      if (this.breathingOffset.x !== 0 || this.breathingOffset.y !== 0) {
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.x += this.breathingOffset.x;
          UltraMode7.camera.y += this.breathingOffset.y;
        }
      }
      
      // Apply camera roll effect
      if (this.cameraRoll !== 0) {
        if (UltraMode7 && UltraMode7.camera) {
          UltraMode7.camera.rotation += this.cameraRoll * (Math.PI / 180);
        }
      }
      
      // Apply letterboxing (would need custom rendering)
      if (this.letterboxHeight > 0) {
        // This would require custom rendering overlay
        // For now, we'll just track the value
      }
    },
    
    // Reset all effects
    reset: function() {
      this.init();
    }
  };
  
  // Camera calculation functions using configuration - OPTIMIZED with caching
  const CameraCalculator = {
    // Cache for altitude calculations to avoid redundant Math operations
    _cachedAltitude: null,
    _cachedAltitudeRatio: null,
    _lastAltitude: null,
    
    // Get cached altitude calculations
    _getCachedAltitude: function(altitude) {
      if (this._lastAltitude !== altitude) {
        this._lastAltitude = altitude;
        this._cachedAltitude = Math.max(AIRSHIP_CONFIG.ALTITUDE.MIN, Math.min(AIRSHIP_CONFIG.ALTITUDE.MAX, altitude));
        this._cachedAltitudeRatio = (this._cachedAltitude - AIRSHIP_CONFIG.ALTITUDE.MIN) / (AIRSHIP_CONFIG.ALTITUDE.MAX - AIRSHIP_CONFIG.ALTITUDE.MIN);
      }
      return { altitude: this._cachedAltitude, ratio: this._cachedAltitudeRatio };
    },
    
    // Calculate dynamic pitch based on altitude
    calculateDynamicPitch: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.PITCH.MIN + (ratio * (CAMERA_CONFIG.PITCH.MAX - CAMERA_CONFIG.PITCH.MIN));
    },
    
    // Calculate dynamic FOV based on altitude
    calculateDynamicFov: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.FOV.MIN + (ratio * (CAMERA_CONFIG.FOV.MAX - CAMERA_CONFIG.FOV.MIN));
    },
    
    // Calculate dynamic camera distance based on altitude
    calculateDynamicCameraDistance: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.DISTANCE.MIN + (ratio * (CAMERA_CONFIG.DISTANCE.MAX - CAMERA_CONFIG.DISTANCE.MIN));
    },
    
    // Calculate dynamic far clip distance based on altitude
    calculateDynamicFarClip: function(altitude) {
      const { ratio } = this._getCachedAltitude(altitude);
      return CAMERA_CONFIG.FAR_CLIP.MIN + (ratio * (CAMERA_CONFIG.FAR_CLIP.MAX - CAMERA_CONFIG.FAR_CLIP.MIN));
    },
    
    // Calculate minimal banking roll based on yaw direction
    calculateBankingRoll: function(currentYaw, previousYaw) {
      if (previousYaw === 0) return 0;
      
      // Calculate yaw change direction
      const yawChange = currentYaw - previousYaw;
      
      // Normalize yaw change to -180 to 180 range
      let normalizedChange = yawChange;
      if (normalizedChange > 180) normalizedChange -= 360;
      if (normalizedChange < -180) normalizedChange += 360;
      
      // Apply minimal roll based on yaw direction
      return (normalizedChange / 180) * CAMERA_CONFIG.BANKING.MAX_ROLL;
    }
  };
  
  // Effect systems using configuration - ENHANCED for more effects
  const EffectSystems = {
  // Ground trail system
    groundTrail: {
      sprites: [],
      creationTimer: 0,
      lastAirshipX: 0,
      lastAirshipY: 0,
      config: {
        lowAltitudeThreshold: AIRSHIP_CONFIG.EFFECTS.TRAIL_LOW_ALTITUDE,
        maxPoints: 25, // Increased from 12 to 25
        fadeTime: 60
      }
    },
    
    // Water ripple system
    waterRipple: {
      sprites: [],
      creationTimer: 0,
      config: {
        lowAltitudeThreshold: AIRSHIP_CONFIG.EFFECTS.RIPPLE_LOW_ALTITUDE
      }
    },
    
    // Wind lines system
    windLines: {
      sprites: [],
      creationTimer: 0,
      config: {
        speedThreshold: 0.1,
        altitudeThreshold: AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE
      }
    },
    
    // Cloud flying system
    cloudFlying: {
      sprites: [],
      backgroundSprites: [],
      foregroundSprites: [],
      creationTimer: 0,
      config: {
        altitudeThreshold: AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE,
        speed: 3.0,
        densityMultiplier: 3.0 // Increased from 1.5 to 3.0
      }
    },
    
    // Dust cloud system
    dustCloud: {
      sprites: [],
      config: {
        threshold: AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD
      }
    }
  };
  
  // Legacy array references for backward compatibility
  let groundTrailSprites = EffectSystems.groundTrail.sprites;
  let waterRippleSprites = EffectSystems.waterRipple.sprites;
  let windLinesSprites = EffectSystems.windLines.sprites;
  let backgroundCloudSprites = EffectSystems.cloudFlying.backgroundSprites;
  let foregroundCloudSprites = EffectSystems.cloudFlying.foregroundSprites;
  let dustCloudSprites = EffectSystems.dustCloud.sprites;
  
  let trailCreationTimer = EffectSystems.groundTrail.creationTimer;
  let rippleCreationTimer = EffectSystems.waterRipple.creationTimer;
  let windLinesCreationTimer = EffectSystems.windLines.creationTimer;
  let cloudCreationTimer = EffectSystems.cloudFlying.creationTimer;
  let lastAirshipX = EffectSystems.groundTrail.lastAirshipX;
  let lastAirshipY = EffectSystems.groundTrail.lastAirshipY;
  
  // Performance optimization variables
  let frameCounter = 0;
  const UPDATE_FREQUENCY = AIRSHIP_CONFIG.PERFORMANCE.UPDATE_FREQUENCY;
  let isAirshipActive = false; // Track if airship effects are currently active
  
  // Visual quality management system
  const VisualQualityManager = {
    // Check if device can handle high-quality effects
    canHandleHighQuality: function() {
      // Simple performance check based on frame rate
      return Graphics.frameRate > 45; // If maintaining good FPS, enable high quality
    },
    
    // Get appropriate effect density based on performance
    getEffectDensity: function() {
      return this.canHandleHighQuality() ? 1.0 : 0.5;
    },
    
    // Get appropriate sprite quality based on performance
    getSpriteQuality: function() {
      return this.canHandleHighQuality() ? 'high' : 'low';
    },
    
    // Apply quality settings to sprites
    applyQualitySettings: function(sprite, quality = 'high') {
      if (quality === 'low') {
        // Reduce visual complexity for better performance
        sprite.scale.set(sprite.scale.x * 0.8, sprite.scale.y * 0.8);
        sprite.opacity = Math.min(sprite.opacity, 180);
      }
    }
  };
  
  // Sprite cleanup system
  const AIRSHIP_EFFECTS = {
    groundTrail: groundTrailSprites,
    waterRipple: waterRippleSprites,
    windLines: windLinesSprites,
    backgroundCloud: backgroundCloudSprites,
    foregroundCloud: foregroundCloudSprites,
    dustCloud: dustCloudSprites
  };
  
  // OPTIMIZATION: Bitmap/Object Pooling System
  const BitmapPool = {
    _pools: {},
    _maxPoolSize: 20,
    _usageStats: {}, // Track usage for leak detection
    _totalCreated: 0,
    _totalReturned: 0,
    
    // Get a bitmap from pool or create new one
    getBitmap: function(key, width, height) {
      if (!this._pools[key]) {
        this._pools[key] = [];
        this._usageStats[key] = { created: 0, returned: 0, active: 0 };
      }
      
      if (this._pools[key].length > 0) {
        const bitmap = this._pools[key].pop();
        bitmap.resize(width, height);
        bitmap.clear();
        this._usageStats[key].active++;
        this._totalCreated++;
        return bitmap;
      }
      
      // Create new bitmap
      const bitmap = new Bitmap(width, height);
      this._usageStats[key].created++;
      this._usageStats[key].active++;
      this._totalCreated++;
      return bitmap;
    },
    
    // Return bitmap to pool
    returnBitmap: function(key, bitmap) {
      if (!this._pools[key]) {
        this._pools[key] = [];
        this._usageStats[key] = { created: 0, returned: 0, active: 0 };
      }
      
      if (this._pools[key].length < this._maxPoolSize) {
        this._pools[key].push(bitmap);
        this._usageStats[key].returned++;
        this._usageStats[key].active--;
        this._totalReturned++;
      } else {
        // Pool is full, destroy the bitmap
        if (bitmap && bitmap.destroy) {
          bitmap.destroy();
        }
        this._usageStats[key].active--;
      }
    },
    
    // Clear all pools
    clear: function() {
      // Destroy all bitmaps in pools
      Object.keys(this._pools).forEach(key => {
        this._pools[key].forEach(bitmap => {
          if (bitmap && bitmap.destroy) {
            bitmap.destroy();
          }
        });
      });
      this._pools = {};
      this._usageStats = {};
      this._totalCreated = 0;
      this._totalReturned = 0;
    },
    
    // Check for memory leaks
    checkForLeaks: function() {
      const leaks = [];
      Object.keys(this._usageStats).forEach(key => {
        const stats = this._usageStats[key];
        const leakRatio = stats.active / Math.max(stats.created, 1);
        if (leakRatio > 0.8) { // If more than 80% of created bitmaps are still active
          leaks.push({
            key: key,
            created: stats.created,
            returned: stats.returned,
            active: stats.active,
            leakRatio: leakRatio
          });
        }
      });
      
      if (leaks.length > 0) {
        console.warn('🚨 BitmapPool memory leak detected:', leaks);
        return leaks;
      }
      
      return null;
    },
    
    // Get pool statistics
    getStats: function() {
      return {
        totalCreated: this._totalCreated,
        totalReturned: this._totalReturned,
        activeBitmaps: this._totalCreated - this._totalReturned,
        pools: Object.keys(this._pools).length,
        usageStats: this._usageStats
      };
    }
  };
  
  ///get off vehicle stuff

const _getOffVehicle = Game_Player.prototype.getOffVehicle;
const _getOnVehicle = Game_Player.prototype.getOnVehicle;

  // Hide airship shadow by overriding shadowOpacity
  const _Game_Vehicle_shadowOpacity = Game_Vehicle.prototype.shadowOpacity;
  Game_Vehicle.prototype.shadowOpacity = function() {
    if (this.isAirship() && $gamePlayer && $gamePlayer.isInAirship()) {
      return 0; // Hide shadow when player is in airship
    }
    return _Game_Vehicle_shadowOpacity.call(this);
  };

  // 1. Block left/right input in airship
  const _Input_isPressed = Input.isPressed;
  
  Input.isPressed = function(key) {
    // Error handling: ensure Input system is available
    if (!Input || typeof Input.isPressed !== 'function') {
      console.warn('⚠️ Input system not available');
      return false;
    }

    // MODIFIED: Don't block left/right input when SAN_AnalogMovement is loaded
    // This allows analog movement to work while still allowing yaw control
    if ($gamePlayer && $gamePlayer.isInAirship()) {
      // Only block down input (we still want left/right for analog movement)
      if (key === 'down') {
        return false;
      }
      // Allow left/right input to pass through to SAN_AnalogMovement
      // The FF6Airship plugin will still handle yaw rotation separately
    }

    return _Input_isPressed.call(this, key);
  };
  
  // 2. Control management system - REWRITTEN for safety
  const ControlManager = {
    // Track control state
    controlsSwapped: false,
    originalKeyMapper: null,
    originalGamepadMapper: null,
    
    // Default RPG Maker MZ input mappings (from rmmz_core.js)
    defaultKeyMapper: {
      9: "tab", // tab
      13: "ok", // enter
      16: "shift", // shift
      17: "control", // control
      18: "control", // alt
      27: "escape", // escape
      32: "ok", // space
      33: "pageup", // pageup
      34: "pagedown", // pagedown
      37: "left", // left arrow
      38: "up", // up arrow
      39: "right", // right arrow
      40: "down", // down arrow
      45: "escape", // insert
      81: "pageup", // Q
      87: "pagedown", // W
      88: "escape", // X
      90: "ok", // Z
      96: "escape", // numpad 0
      98: "down", // numpad 2
      100: "left", // numpad 4
      102: "right", // numpad 6
      104: "up", // numpad 8
      120: "debug" // F9
    },
    
    defaultGamepadMapper: {
      0: "ok", // A
      1: "cancel", // B
      2: "shift", // X
      3: "menu", // Y
      4: "pageup", // LB
      5: "pagedown", // RB
      12: "up", // D-pad up
      13: "down", // D-pad down
      14: "left", // D-pad left
      15: "right" // D-pad right
    },
    
    // Initialize the control manager
    initialize: function() {
      // Store the current mappings (which should be the defaults)
      this.originalKeyMapper = { ...Input.keyMapper };
      this.originalGamepadMapper = { ...Input.gamepadMapper };
      console.log('🎮 ControlManager initialized with default mappings');
    },
    
    // SAFE: Swap controls for airship (creates new objects instead of modifying)
    swapControls: function() {
      if (this.controlsSwapped) return;
      
      // Create new mapper objects with airship controls
      const airshipKeyMapper = { ...this.originalKeyMapper };
      const airshipGamepadMapper = { ...this.originalGamepadMapper };
      
      // Apply airship-specific mappings - swap whatever actions are currently mapped
      const currentUpAction = airshipKeyMapper[38];
      const currentShiftAction = airshipKeyMapper[16];
      
      airshipKeyMapper[16] = currentUpAction;     // Current up action → shift key
      airshipKeyMapper[38] = currentShiftAction;  // Current shift action → up key
      
      // Also swap gamepad mappings if they exist
      if (airshipGamepadMapper[2] && airshipGamepadMapper[12]) {
        const currentGamepadUp = airshipGamepadMapper[12];
        const currentGamepadShift = airshipGamepadMapper[2];
        airshipGamepadMapper[2] = currentGamepadUp;     // Current up action → X button
        airshipGamepadMapper[12] = currentGamepadShift; // Current shift action → D-pad up
      }
      
      // Replace the mappers
      Input.keyMapper = airshipKeyMapper;
      Input.gamepadMapper = airshipGamepadMapper;
      
      this.controlsSwapped = true;
      console.log('🎮 Airship controls activated (respects user bindings)');
    },
    
    // SAFE: Restore original controls
    restoreControls: function() {
      if (!this.controlsSwapped) return;
      
      // Restore original mappings
      Input.keyMapper = { ...this.originalKeyMapper };
      Input.gamepadMapper = { ...this.originalGamepadMapper };
      
      this.controlsSwapped = false;
      console.log('🎮 Airship controls deactivated (safe mode)');
    },
    
    // EMERGENCY: Force restore to original state (respects user bindings)
    forceRestoreControls: function() {
      console.log('🚨 Force restoring controls to original state...');
      
      // Restore to original state (which respects user's custom bindings)
      if (this.originalKeyMapper && this.originalGamepadMapper) {
        Input.keyMapper = { ...this.originalKeyMapper };
        Input.gamepadMapper = { ...this.originalGamepadMapper };
      }
      
      this.controlsSwapped = false;
      console.log('✅ Controls force restored to original state');
    },
    
    // Check if controls need to be updated
    updateControls: function() {
      const isInAirship = $gamePlayer && $gamePlayer.isInAirship();
      
      if (isInAirship && !this.controlsSwapped) {
        this.swapControls();
      } else if (!isInAirship && this.controlsSwapped) {
        this.restoreControls();
      }
    },
    
    // Validate that controls are in a good state
    validateControls: function() {
      // Don't interfere with user's custom key bindings from VisuMZ_1_OptionsCore
      // Just check that the mappings exist and are valid
      const hasUpKey = Input.keyMapper[38] !== undefined;
      const hasShiftKey = Input.keyMapper[16] !== undefined;
      
      if (!hasUpKey || !hasShiftKey) {
        console.warn('⚠️ Missing key mappings - this should not happen');
        return false;
      }
      
      return true;
    }
  };
  
  // Initialize ControlManager when plugin loads
  ControlManager.initialize();
  
  // Expose ControlManager globally for testing
  window.ControlManager = ControlManager;
  
  // Add safety check on game start (but don't force defaults)
  const _Scene_Boot_start = Scene_Boot.prototype.start;
  Scene_Boot.prototype.start = function() {
    _Scene_Boot_start.call(this);
    
    // Just validate that mappings exist (don't force defaults)
    setTimeout(() => {
      if (ControlManager && ControlManager.validateControls) {
        ControlManager.validateControls();
      }
    }, 1000);
  };
  
  // 3. Yaw, facing, and pitch logic
  const _SceneMap_update = Scene_Map.prototype.update;
  Scene_Map.prototype.update = function() {
	
	/// code for swapping controls while on airship	
	  _SceneMap_update.call(this);

	  // Error handling: ensure required objects exist
	  if (!$gamePlayer || !$gameMap || !window.UltraMode7) {
	    console.warn('⚠️ Required objects not available for airship controls');
	    return;
	  }

	  // SAFETY: Validate controls are in good state
	  if (ControlManager && ControlManager.validateControls) {
	    ControlManager.validateControls();
	  }

	  // Update control mappings using ControlManager
	  ControlManager.updateControls();
	
    if (!$gamePlayer || !window.UltraMode7) return;

    // Error handling: ensure UltraMode7 methods exist
    if (typeof UltraMode7.getYaw !== 'function' || typeof UltraMode7.getPitch !== 'function') {
      console.warn('⚠️ UltraMode7 methods not available');
      return;
    }

    const currentYaw = UltraMode7.getYaw();
    const currentPitch = UltraMode7.getPitch();
	let yawZero = false
	

    // Yaw & facing logic
    
		
if ($gamePlayer.isInAirship()) {
  // Error handling: ensure $gameTemp exists
  if (!$gameTemp) {
    console.warn('⚠️ $gameTemp not available');
    return;
  }
  
  if ($gameTemp._yawFacing === undefined) {
    $gameTemp._yawFacing = UltraMode7.getYaw();
  }
  
  

  // Only allow yaw to be updated from $gameTemp while NOT landing
  if (!pitchResetActive) {
    UltraMode7.setYaw($gameTemp._yawFacing);
	
	if (_Input_isPressed.call(Input, 'shift')) {
      // Error handling: ensure airship exists and has altitude property
      const airship = $gameMap.airship();
      if (!airship || typeof airship._altitude !== 'number') {
        console.warn('⚠️ Airship or altitude not available');
        return;
      }
      
      if (airship._altitude < AIRSHIP_CONFIG.ALTITUDE.MAX) {
        AltitudeManager.updateAltitude(airship, airship._altitude + 1);
        // Update all dynamic camera parameters in real-time when altitude changes
        const currentAltitude = $gameMap.airship()._altitude;
        const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
        const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
        const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
        const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
        
        UltraMode7.animatePitch(dynamicPitch, 10); // Smooth transition over 10 frames
        UltraMode7.animateFov(dynamicFov, 10);
        UltraMode7.animateCameraDistance(dynamicCameraDistance, 10);
        UltraMode7.animateFarClipZ(dynamicFarClip, 10);
      }
	}  
	
	if (_Input_isPressed.call(Input, 'down')) {
      // Error handling: ensure airship exists and has altitude property
      const airship = $gameMap.airship();
      if (!airship || typeof airship._altitude !== 'number') {
        console.warn('⚠️ Airship or altitude not available');
        return;
      }
      
      if (airship._altitude > AIRSHIP_CONFIG.ALTITUDE.MIN) {
        AltitudeManager.updateAltitude(airship, airship._altitude - 1.5);
        // Update all dynamic camera parameters in real-time when altitude changes
        const currentAltitude = $gameMap.airship()._altitude;
        const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
        const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
        const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
        const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
        
        UltraMode7.animatePitch(dynamicPitch, 10); // Smooth transition over 10 frames
        UltraMode7.animateFov(dynamicFov, 10);
        UltraMode7.animateCameraDistance(dynamicCameraDistance, 10);
        UltraMode7.animateFarClipZ(dynamicFarClip, 10);
      }
	}  
	



// left right (now works with our SAN_AnalogMovement compatibility layer)
    if (_Input_isPressed.call(Input, 'left')) {
      $gameTemp._yawFacing = ($gameTemp._yawFacing + 2) % 360;
      // Apply banking roll when turning (always tilt)
      UltraMode7.animateRoll(-2, 15); // Tilt left when turning left
      lastYawDirection = $gameTemp._yawFacing;
    }

    if (_Input_isPressed.call(Input, 'right')) {
      $gameTemp._yawFacing = ($gameTemp._yawFacing + 358) % 360;
      // Apply banking roll when turning (always tilt)
      UltraMode7.animateRoll(2, 15);  // Tilt right when turning right
      lastYawDirection = $gameTemp._yawFacing;
	}

			// Reset roll when not turning (like original)
		if (!_Input_isPressed.call(Input, 'left') && !_Input_isPressed.call(Input, 'right')) {
			UltraMode7.animateRoll(0, 15);
	}
		
	// Don't apply roll to parallax - keep it level
	_parallaxRotation = 0;

		

  if (!Spriteset_Map._mauiWrappedParallax) {
    Spriteset_Map._mauiWrappedParallax = true;

    const _updateParallax = Spriteset_Map.prototype.updateParallax;

    Spriteset_Map.prototype.updateParallax = function () {
      _updateParallax.call(this);

      const parallax = this._parallax;
      if (parallax && parallax instanceof TilingSprite) {
        // Expand the tiling area (bigger than the screen so rotation doesn't show edges)
        parallax.move(0, 0, Graphics.width * 2.1, Graphics.height * 2.1);

        // Center the tiling pattern visually
        parallax.anchor.set(0.5, 0.05);
        parallax.x = Graphics.width / 2;

        // Removed pitch-based parallax movement to keep it locked to the scene
        // const pitchParallax = (-Graphics.height / 3) * (1 - (UltraMode7.getPitch() - 45) / 10);
        // parallax.y = pitchParallax;

        parallax.scale.x = 0.5;
        parallax.scale.y = 0.5;

        parallax.rotation = _parallaxRotation * (Math.PI / 180);

        if (parallax.bitmap) {
          parallax.origin.x = -(UltraMode7.getYaw() * (parallax.bitmap.width / 180)) % parallax.bitmap.width;
        }
      }
    };
  }



  }
}

	  
	if (pitchSetActive) {
		
	  Game_Player.prototype.setDirection = function() { this._direction = 8; };
      Game_Follower.prototype.setDirection = function() { this._direction = 8; };	
	
		UltraMode7.animateCameraY(targetCameraY, 60);
		
		// Use dynamic camera parameters based on current altitude
		const currentAltitude = $gameMap.airship()._altitude || AIRSHIP_CONFIG.ALTITUDE.MIN;
		const dynamicPitch = CameraCalculator.calculateDynamicPitch(currentAltitude);
		const dynamicFov = CameraCalculator.calculateDynamicFov(currentAltitude);
		const dynamicCameraDistance = CameraCalculator.calculateDynamicCameraDistance(currentAltitude);
		const dynamicFarClip = CameraCalculator.calculateDynamicFarClip(currentAltitude);
		
		UltraMode7.animatePitch(dynamicPitch, 60);
		UltraMode7.animateFov(dynamicFov, 60);
		UltraMode7.animateCameraDistance(dynamicCameraDistance, 60);
		UltraMode7.animateFarClipZ(dynamicFarClip, 60);

		pitchSetActive = false;

    } 

	if (pitchResetActive) {
		   

		
		if (!$gamePlayer.isInAirship()){

		$gameTemp._yawFacing = 0;
		pitchResetActive = false;
		}

    }
	


Game_Player.prototype.getOnVehicle = function() {
  const result = _getOnVehicle.call(this);
  if (this.vehicle() && this.vehicle().isAirship()) {
    console.log("🚀 Airship takeoff: setting pitch to flying value.");

    // Initialize altitude for airship
    const airship = this.vehicle();
    AltitudeManager.initializeAltitude(airship);

    // Load SAN_AnalogMovement for airship
    loadSANAnalogMovement();
    console.log("✈️ SAN_AnalogMovement loaded for airship");

    pitchSetActive = true;
    wasFlying = true;
  }
  return result;
};

Game_Player.prototype.getOffVehicle = function() {
  const result = _getOffVehicle.call(this);

  if (this.vehicle().isAirship()) {
    const x = this.x;
    const y = this.y;
    if ($gameMap.isAirshipLandOk(x, y)) {

		// Unload SAN_AnalogMovement when leaving airship
		unloadSANAnalogMovement();
		console.log("🛬 SAN_AnalogMovement unloaded");

		delete Game_Player.prototype.setDirection;
		delete Game_Follower.prototype.setDirection;

		Input._currentState['up'] = false;
		
		const landAnimationSpeed = 60; // turns airship back to world map settings
		UltraMode7.animateRoll(0,landAnimationSpeed);
		
		if (UltraMode7.getYaw() > 180) {
		  UltraMode7.animateYaw(360, landAnimationSpeed);
		  setTimeout(() => {
			UltraMode7.setYaw(0);
		  }, (landAnimationSpeed) * 16.67);
		} else {
		  UltraMode7.animateYaw(0, landAnimationSpeed);
		}
		
		UltraMode7.animateCameraY(defaultCameraY, landAnimationSpeed);
		UltraMode7.animatePitch(defaultPitch, landAnimationSpeed);
		 
		$gamePlayer.setDirection(4);        
		$gamePlayer._realDirection = 4;
	
      pitchResetActive = true;
      console.log("✅ Tile OK for landing — pitch reset active.");
    } else {
      console.log("❌ Tile not landable — skipping pitch reset.");
    }
  }

  return result;
};



	
  };
  
  
  // 🆕 4. Altitude tracking and visual system
  const AltitudeManager = {
    // Initialize altitude for airship
    initializeAltitude: function(airship) {
      if (!airship || typeof airship._altitude !== 'number') {
        airship._altitude = AIRSHIP_CONFIG.ALTITUDE.MIN;
        console.log('📊 Airship altitude initialized to', airship._altitude);
      }
    },
    
    // Validate altitude value
    validateAltitude: function(altitude) {
      return Math.max(AIRSHIP_CONFIG.ALTITUDE.MIN, 
                     Math.min(AIRSHIP_CONFIG.ALTITUDE.MAX, altitude));
    },
    
    // Update altitude with validation
    updateAltitude: function(airship, newAltitude) {
      if (!airship) return;
      
      const validatedAltitude = this.validateAltitude(newAltitude);
      airship._altitude = validatedAltitude;
      
      return validatedAltitude;
    },
    
    // Get current altitude safely
    getCurrentAltitude: function(airship) {
      if (!airship || typeof airship._altitude !== 'number') {
        return AIRSHIP_CONFIG.ALTITUDE.MIN;
      }
      return airship._altitude;
    }
  };
  
  // Sprite draw override to apply altitude visually
  (function () {
    const _updateMode7Transform = Sprite_Character.prototype.updateMode7Transform;
    Sprite_Character.prototype.updateMode7Transform = function () {
      _updateMode7Transform.call(this);

      if (this._character === $gameMap.airship()) {
        const altitude = AltitudeManager.getCurrentAltitude(this._character);
        this.y -= altitude;
      }
    };
  })();
  
  // Ground Trail Sprite Class
  class GroundTrailSprite extends Sprite {
    constructor(x, y, airshipDirection) {
      super();
      this.x = x;
      this.y = y;
      this.opacity = 255;
      this.fadeTimer = EffectSystems.groundTrail.config.fadeTime;
      this.driftSpeed = 3.0 + (Math.random() - 0.5) * 2; // Random speed variation
      this.driftDirection = airshipDirection; // Direction the airship was moving
      this.horizontalDrift = (Math.random() - 0.5) * 2.5; // Fixed horizontal drift per particle
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'trail'; // Track bitmap key for cleanup
      this.createTrailBitmap();
    }
    
    createTrailBitmap() {
      const size = 2 + Math.floor(Math.random() * 2); // Reduced size between 2-3
      const bitmap = BitmapPool.getBitmap('trail', size, size);
      
      // OPTIMIZATION: Pre-calculate distance values to avoid repeated Math.sqrt
      const center = size / 2;
      const maxDistanceSquared = center * center; // Square of max distance
      
      // Create a circular white particle
      for (let i = 0; i < size; i++) {
        for (let j = 0; j < size; j++) {
          const distanceSquared = (i - center) ** 2 + (j - center) ** 2;
          if (distanceSquared <= maxDistanceSquared) {
            bitmap.fillRect(i, j, 1, 1, '#FFFFFF');
          }
        }
      }
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / EffectSystems.groundTrail.config.fadeTime) * 255);
      
      // Always add downward drift to simulate gravity
      this.y += this.driftSpeed * 0.8;
      
      // Add fixed horizontal drift for this particle
      this.x += this.horizontalDrift;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Dust Cloud Sprite Class
  class DustCloudSprite extends Sprite {
    constructor(x, y, airshipDirection) {
      super();
      this.x = x;
      this.y = y;
      this.opacity = 255;
      this.fadeTimer = EffectSystems.groundTrail.config.fadeTime;
      this.driftSpeed = 3.0 + (Math.random() - 0.5) * 2; // Random speed variation
      this.driftDirection = airshipDirection; // Direction the airship was moving
      this.horizontalDrift = (Math.random() - 0.5) * 2.5; // Fixed horizontal drift per particle
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'dust'; // Track bitmap key for cleanup
      this.createDustBitmap();
    }
    
    createDustBitmap() {
      const size = 2 + Math.floor(Math.random() * 2); // Reduced size between 2-3
      const bitmap = BitmapPool.getBitmap('dust', size, size);
      
      // OPTIMIZATION: Pre-calculate distance values to avoid repeated Math.sqrt
      const center = size / 2;
      const maxDistanceSquared = center * center; // Square of max distance
      
      // Create a circular brown particle (exactly like white trail but brown)
      for (let i = 0; i < size; i++) {
        for (let j = 0; j < size; j++) {
          const distanceSquared = (i - center) ** 2 + (j - center) ** 2;
          if (distanceSquared <= maxDistanceSquared) {
            bitmap.fillRect(i, j, 1, 1, '#8B7355'); // Brown dust color
          }
        }
      }
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / EffectSystems.groundTrail.config.fadeTime) * 255);
      
      // Always add downward drift to simulate gravity
      this.y += this.driftSpeed * 0.8;
      
      // Add fixed horizontal drift for this particle
      this.x += this.horizontalDrift;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Grass Disturbance Sprite Class
  class GrassDisturbanceSprite extends Sprite {
    constructor(x, y) {
      super();
      this.x = x;
      this.y = y;
      this.opacity = 255;
      this.fadeTimer = 120;
      this.waveTimer = 0;
      this.createGrassBitmap();
    }
    
    createGrassBitmap() {
      const width = 24 + Math.floor(Math.random() * 12); // Reduced width
      const height = 10 + Math.floor(Math.random() * 6); // Reduced height
      const bitmap = new Bitmap(width, height);
      const ctx = bitmap.context;
      
      // Create grass disturbance effect
      ctx.fillStyle = '#228B22'; // Forest green
      ctx.globalAlpha = 0.7;
      
      // Draw wavy grass lines
      for (let i = 0; i < 8; i++) {
        const x = (i / 7) * width;
        const waveHeight = 3 + Math.random() * 4;
        const baseY = height * 0.3 + Math.random() * height * 0.4;
        
        ctx.beginPath();
        ctx.moveTo(x, baseY);
        ctx.quadraticCurveTo(x + width * 0.1, baseY - waveHeight, x + width * 0.2, baseY);
        ctx.quadraticCurveTo(x + width * 0.3, baseY + waveHeight, x + width * 0.4, baseY);
        ctx.quadraticCurveTo(x + width * 0.5, baseY - waveHeight, x + width * 0.6, baseY);
        ctx.quadraticCurveTo(x + width * 0.7, baseY + waveHeight, x + width * 0.8, baseY);
        ctx.quadraticCurveTo(x + width * 0.9, baseY - waveHeight, x + width, baseY);
        ctx.stroke();
      }
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.waveTimer++;
      this.opacity = Math.max(0, (this.fadeTimer / 120) * 255);
      
      // Animate grass wave
      this.scale.x = 1 + Math.sin(this.waveTimer * 0.2) * 0.1;
      this.scale.y = 1 + Math.cos(this.waveTimer * 0.15) * 0.05;
      
      if (this.fadeTimer <= 0) {
        this.destroy();
      }
    }
  }
  
  // Wind Lines Sprite Class
  class WindLinesSprite extends Sprite {
    constructor(x, y, direction) {
      super();
      this.x = x;
      this.y = y;
      this.direction = direction; // 0-360 degrees
      this.opacity = 200; // Higher opacity
      this.fadeTimer = 25; // Shorter lifetime for better performance
      this.length = 40 + Math.random() * 30; // Shorter lines for better performance
      this.width = 1 + Math.random() * 1; // Thinner lines
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'windLine'; // Track bitmap key for cleanup
      this.createWindLineBitmap();
    }
    
    createWindLineBitmap() {
      const bitmap = BitmapPool.getBitmap('windLine', this.length, this.width);
      const ctx = bitmap.context;
      
      // Create a wind line effect
      ctx.fillStyle = '#FFFFFF'; // White wind lines
      ctx.globalAlpha = 1.0; // Full opacity
      
      // Draw the wind line
      ctx.fillRect(0, 0, this.length, this.width);
      
      this.bitmap = bitmap;
      this.anchor.set(0, 0.5); // Anchor at left center
      this.rotation = this.direction * (Math.PI / 180); // Rotate to radial direction
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / 25) * 200); // Use shorter timer
      
      // Move outward from center
      const speed = 2.5 + Math.random() * 1.5; // Slightly slower for better performance
      const angle = this.direction * (Math.PI / 180);
      this.x += Math.cos(angle) * speed;
      this.y += Math.sin(angle) * speed;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Inner Wind Lines Sprite Class (more transparent, closer to center)
  class InnerWindLinesSprite extends Sprite {
    constructor(x, y, direction) {
      super();
      this.x = x;
      this.y = y;
      this.direction = direction; // 0-360 degrees
      this.opacity = 60; // Even lower opacity for better performance
      this.fadeTimer = 20; // Shorter lifetime
      this.length = 30 + Math.random() * 20; // Shorter lines for better performance
      this.width = 1; // Fixed thin width
      this._destroyed = false; // Track destruction state
      this._bitmapKey = 'windLine'; // Track bitmap key for cleanup
      this.createWindLineBitmap();
    }
    
    createWindLineBitmap() {
      const bitmap = BitmapPool.getBitmap('windLine', this.length, this.width);
      const ctx = bitmap.context;
      
      // Create a wind line effect
      ctx.fillStyle = '#FFFFFF'; // White wind lines
      ctx.globalAlpha = 0.5; // Lower alpha for transparency
      
      // Draw the wind line
      ctx.fillRect(0, 0, this.length, this.width);
      
      this.bitmap = bitmap;
      this.anchor.set(0, 0.5); // Anchor at left center
      this.rotation = this.direction * (Math.PI / 180); // Rotate to radial direction
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / 20) * 60); // Use lower opacity
      
      // Move outward from center
      const speed = 1.5 + Math.random() * 1.5; // Slower for better performance
      const angle = this.direction * (Math.PI / 180);
      this.x += Math.cos(angle) * speed;
      this.y += Math.sin(angle) * speed;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
    
    // FIX: Proper cleanup to prevent memory leaks
    destroy(options) {
      // Return bitmap to pool before destroying sprite
      if (this.bitmap && this._bitmapKey) {
        BitmapPool.returnBitmap(this._bitmapKey, this.bitmap);
        this.bitmap = null;
      }
      super.destroy(options);
    }
  }
  
  // Water Ripple Sprite Class
  class WaterRippleSprite extends Sprite {
    constructor(x, y) {
      super();
      this.x = x;
      this.y = y;
      this.opacity = 255; // Increased opacity
      this.fadeTimer = 60; // Half the lifetime
      this.expansionRate = 1.5; // Reduced expansion rate
      this.driftSpeed = 4.0 + (Math.random() - 0.5) * 2; // Faster downward drift
      this._destroyed = false; // Track destruction state
      this.createRippleBitmap();
    }
    
    createRippleBitmap() {
      const size = 48; // Larger size for better detail
      const bitmap = new Bitmap(size, size);
      const ctx = bitmap.context;
      
      // Create multiple concentric ripples for more natural look
      const rippleCount = 3;
      const baseRadius = size / 4;
      
      for (let i = 0; i < rippleCount; i++) {
        const radius = baseRadius + (i * 2);
        const alpha = 0.8 - (i * 0.2); // Fade outer rings
        
      ctx.strokeStyle = '#87CEEB'; // Sky blue water color
        ctx.lineWidth = 1.5; // Thinner lines
        ctx.globalAlpha = alpha;
      
        // Draw multiple concentric circles
      ctx.beginPath();
        ctx.arc(size/2, size/2, radius, 0, Math.PI * 2);
      ctx.stroke();
      }
      
      // Add a subtle inner highlight
      ctx.fillStyle = '#FFFFFF';
      ctx.globalAlpha = 0.3;
      ctx.beginPath();
      ctx.arc(size/2, size/2, baseRadius - 2, 0, Math.PI * 2);
      ctx.fill();
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
      this.scale.set(0.3, 0.3); // Start with slightly larger scale
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      
      // More natural fade curve (faster at start, slower at end)
      const fadeProgress = this.fadeTimer / 60;
      this.opacity = Math.max(0, Math.pow(fadeProgress, 1.5) * 255);
      
      // Expand the ripple with easing (slower expansion over time)
      const expansionProgress = 1 - fadeProgress;
      const expansionEasing = Math.pow(expansionProgress, 0.7);
      this.scale.x = 0.3 + (this.expansionRate * expansionEasing);
      this.scale.y = 0.3 + (this.expansionRate * expansionEasing);
      
      // Add subtle downward drift
      this.y += this.driftSpeed * 0.5; // Reduced drift speed
      
      // Add slight rotation for more natural movement
      this.rotation += 0.02;
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
  }
  
  // Water Spray Sprite Class
  class WaterSpraySprite extends Sprite {
    constructor(x, y, intensity = 1.0) {
      super();
      this.x = x;
      this.y = y;
      this.intensity = intensity;
      this.opacity = 200;
      this.fadeTimer = 75; // Shorter fade time for water spray
      this.gravity = 0.3;
      this.velocityX = (Math.random() - 0.5) * 4;
      this.velocityY = -Math.random() * 3 - 1;
      this.createWaterSprayBitmap();
    }
    
    createWaterSprayBitmap() {
      const size = 4 + Math.floor(Math.random() * 4); // Slightly larger for better detail
      const bitmap = new Bitmap(size, size);
      const ctx = bitmap.context;
      
      // Create more realistic water droplet with gradient
      const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
      gradient.addColorStop(0, '#FFFFFF'); // Bright center
      gradient.addColorStop(0.3, '#B0E0E6'); // Light blue
      gradient.addColorStop(0.7, '#87CEEB'); // Sky blue
      gradient.addColorStop(1, '#4682B4'); // Steel blue edge
      
      ctx.fillStyle = gradient;
      ctx.globalAlpha = 0.9;
      
      // Draw water droplet with smooth edges
      ctx.beginPath();
      ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
      ctx.fill();
      
      // Add bright highlight for realism
      ctx.fillStyle = '#FFFFFF';
      ctx.globalAlpha = 0.8;
      ctx.beginPath();
      ctx.arc(size/2 - 1, size/2 - 1, size/3, 0, Math.PI * 2);
      ctx.fill();
      
      // Add smaller secondary highlight
      ctx.fillStyle = '#FFFFFF';
      ctx.globalAlpha = 0.4;
      ctx.beginPath();
      ctx.arc(size/2 - 0.5, size/2 - 0.5, size/6, 0, Math.PI * 2);
      ctx.fill();
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5);
      this.scale.set(this.intensity, this.intensity);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      
      // More natural fade curve for water spray
      const fadeProgress = this.fadeTimer / 75;
      this.opacity = Math.max(0, Math.pow(fadeProgress, 1.2) * 200);
      
      // Apply gravity with air resistance
      this.velocityY += this.gravity;
      this.x += this.velocityX;
      this.y += this.velocityY;
      
      // Add air resistance (slow down over time)
      this.velocityX *= 0.95;
      this.velocityY *= 0.99;
      
      // Add slight rotation for more natural movement
      this.rotation += 0.05;
      
      // Scale down slightly as it falls (perspective effect)
      const scaleReduction = 1 - (fadeProgress * 0.3);
      this.scale.set(this.intensity * scaleReduction, this.intensity * scaleReduction);
      
      if (this.fadeTimer <= 0) {
        this._destroyed = true;
        this.destroy();
      }
    }
  }
  
  // Cloud Sprite Class for high-altitude cloud flying effect
  class CloudSprite extends Sprite {
    constructor(x, y, size, opacity, direction, speedMultiplier = 1.0) {
      super();
      this.x = x;
      this.y = y;
      this.initialSize = size;
      this.size = size;
      this.opacity = opacity;
      this.direction = direction; // Direction toward center
      this.fadeTimer = 120; // Longer lifetime for clouds
      this.speed = (10.0 + Math.random() * 10.0) * speedMultiplier; // Speed with multiplier
      this.airshipX = Graphics.width / 2; // Will be updated each frame
      this.airshipY = Graphics.height / 2; // Will be updated each frame
      this._destroyed = false; // Track destruction state
      this._lastX = x; // Track previous position for motion blur prevention
      this._lastY = y;
      this.createCloudBitmap();
      
      // Apply quality settings
      VisualQualityManager.applyQualitySettings(this, VisualQualityManager.getSpriteQuality());
    }
    
    createCloudBitmap() {
      const bitmap = new Bitmap(this.size, this.size);
      const ctx = bitmap.context;
      
      // Create a soft, fluffy cloud effect
      ctx.fillStyle = '#FFFFFF';
      ctx.globalAlpha = this.opacity / 255;
      
      // Draw multiple overlapping circles to create horizontally oval cloud shape
      const centerX = this.size / 2;
      const centerY = this.size / 2;
      const radius = this.size / 3;
      
      // Main cloud body (horizontally stretched)
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fill();
      
      // Additional cloud puffs (more horizontal spread)
      ctx.beginPath();
      ctx.arc(centerX - radius * 0.8, centerY, radius * 0.6, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX + radius * 0.8, centerY, radius * 0.6, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX - radius * 1.2, centerY, radius * 0.5, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX + radius * 1.2, centerY, radius * 0.5, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX, centerY - radius * 0.2, radius * 0.4, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(centerX, centerY + radius * 0.2, radius * 0.4, 0, Math.PI * 2);
      ctx.fill();
      
      this.bitmap = bitmap;
      this.anchor.set(0.5, 0.5); // Center anchor
      
      // Set initial scale to 0 (clouds start invisible and grow from nothing)
      this.scale.set(0, 0);
    }
    
    update() {
      super.update();
      this.fadeTimer--;
      this.opacity = Math.max(0, (this.fadeTimer / 120) * this.opacity);
      
      // Update airship position for this cloud
      const airship = $gameMap.airship();
      if (airship) {
        this.airshipX = airship.screenX();
        this.airshipY = airship.screenY();
      }
      
      // Store previous position for motion blur prevention
      this._lastX = this.x;
      this._lastY = this.y;
      
      // Move clouds in radial flow pattern with smooth interpolation
      const targetX = this.x + Math.cos(this.direction) * this.speed;
      const targetY = this.y + Math.sin(this.direction) * this.speed;
      
      // Smooth movement to prevent visual glitches
      this.x = this.x + (targetX - this.x) * 0.8;
      this.y = this.y + (targetY - this.y) * 0.8;
      
      // Calculate distance from horizon for scaling (clouds get larger as they move away from horizon)
      const horizonY = Graphics.height * 0.7;
      const distanceFromHorizon = horizonY - this.y; // Positive = further from horizon
      const maxDistance = Graphics.height * 0.3; // Maximum distance from horizon
      const scaleFactor = 1 + (distanceFromHorizon / maxDistance) * -2 // Scale as moving away from horizon
      
      // Scale the cloud as it flows down (gets larger)
      this.scale.set(scaleFactor, scaleFactor);
      
      // Destroy if too far down or off-screen
      if (this.y > Graphics.height + 100 || this.x < -100 || this.x > Graphics.width + 100 || 
          this.y < -100) {
        this._destroyed = true;
        this.destroy();
      }
    }
  }
  
  // Ground trail management with proper z-index layering
  const _SceneMap_createDisplayObjects = Scene_Map.prototype.createDisplayObjects;
  Scene_Map.prototype.createDisplayObjects = function() {
    _SceneMap_createDisplayObjects.call(this);
    
    // Create layered sprite system for proper rendering order
    this._airshipEffectLayers = {
      // Background effects (water ripples, ground trails)
      background: new Sprite(),
      // Mid-level effects (dust clouds, wind lines)
      midground: new Sprite(),
      // Foreground effects (clouds)
      foreground: new Sprite()
    };
    
    // Set proper z-indices to avoid conflicts
    this._airshipEffectLayers.background.z = 1;  // Below characters
    this._airshipEffectLayers.midground.z = 3;   // Above characters
    this._airshipEffectLayers.foreground.z = 5;  // Above UI elements
    
    // Add layers to scene
    Object.values(this._airshipEffectLayers).forEach(layer => {
      this.addChild(layer);
    });
    
    // Legacy layer references for backward compatibility
    this._trailLayer = this._airshipEffectLayers.background;
    this._effectsLayer = this._airshipEffectLayers.midground;
  };
  
  // Extend the existing Scene_Map update function to include trail updates
  const _SceneMap_update_original = Scene_Map.prototype.update;
  Scene_Map.prototype.update = function() {
    _SceneMap_update_original.call(this);
    
    // Performance optimization: only update effects every few frames
    frameCounter++;
    
    // Update ground trail and low-altitude effects - ONLY when 'up' is pressed
    if ($gamePlayer && $gamePlayer.isInAirship() && !pitchSetActive && Input.isPressed('up')) {
      // Track airship state for cleanup
      if (!isAirshipActive) {
        isAirshipActive = true;
        if (DEBUG_MODE) console.log('🚀 Airship effects activated (up key pressed)');
      }
      
      // Update effects with frame skipping for performance
      if (frameCounter % UPDATE_FREQUENCY === 0) {
        // Apply dynamic quality settings based on performance
        const quality = VisualQualityManager.getSpriteQuality();
        const density = VisualQualityManager.getEffectDensity();
        
        // Update effects with quality consideration
        this.updateGroundTrail(density);
        this.updateLowAltitudeEffects(density);
        this.updateCloudFlying(density);
        
        // NEW: Update advanced camera effects
        if ($gameMap && $gameMap.airship()) {
          const airship = $gameMap.airship();
          const altitude = airship._altitude || 0;
          
          // Update airship banking/tilting
          AirshipBanking.updateTilt(airship);
          
          AdvancedCameraEffects.updateShake(airship, altitude);
          AdvancedCameraEffects.updateDepthOfField(altitude);
          AdvancedCameraEffects.updateZoom(airship, altitude);
          AdvancedCameraEffects.updateMotionBlur(airship);
          AdvancedCameraEffects.updateLetterbox(airship, altitude);
          AdvancedCameraEffects.updateCinematic(airship, altitude);
          AdvancedCameraEffects.updateDollyZoom(altitude);
          AdvancedCameraEffects.updateDutchAngle(airship);
          AdvancedCameraEffects.updateWhipPan(airship);
          AdvancedCameraEffects.updateBreathing();
          AdvancedCameraEffects.updateSlowMotion(airship);
          AdvancedCameraEffects.updateCameraRoll(airship);
          AdvancedCameraEffects.updateBulletTime(airship);
          AdvancedCameraEffects.updateTeleport(altitude);
          AdvancedCameraEffects.updateGravityDefy(altitude);
          AdvancedCameraEffects.applyEffects();
        }
      }
      
      // Always run cleanup (but less frequently)
      if (frameCounter % (UPDATE_FREQUENCY * 3) === 0) {
        this.cleanupDestroyedSprites();
      }
    } else {
      // Airship is not active, ensure cleanup
      if (isAirshipActive) {
        isAirshipActive = false;
        this.cleanupAllAirshipEffects();
        if (DEBUG_MODE) console.log('🛬 Airship effects deactivated');
      }
    }
  };
  
  // Comprehensive sprite cleanup system - OPTIMIZED with efficient array operations
  Scene_Map.prototype.cleanupDestroyedSprites = function() {
    // FIX: Properly destroy sprites instead of just removing references
    const cleanupArray = (array) => {
      const validSprites = [];
      for (let i = 0; i < array.length; i++) {
        const sprite = array[i];
        if (sprite && sprite.parent && !sprite._destroyed) {
          validSprites.push(sprite);
        } else if (sprite && sprite._destroyed) {
          // FIX: Properly destroy sprites that are marked as destroyed
          if (sprite.parent) {
            sprite.parent.removeChild(sprite);
          }
          if (sprite.destroy) {
            sprite.destroy();
          }
        }
      }
      return validSprites;
    };
    
    // Clean up all sprite arrays by removing destroyed sprites
    groundTrailSprites = cleanupArray(groundTrailSprites);
    waterRippleSprites = cleanupArray(waterRippleSprites);
    windLinesSprites = cleanupArray(windLinesSprites);
    backgroundCloudSprites = cleanupArray(backgroundCloudSprites);
    foregroundCloudSprites = cleanupArray(foregroundCloudSprites);
    dustCloudSprites = cleanupArray(dustCloudSprites);
    
    // Emergency cleanup if arrays grow too large
    const maxSpritesPerType = AIRSHIP_CONFIG.PERFORMANCE.MAX_SPRITES_PER_TYPE;
    if (groundTrailSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many ground trail sprites, forcing cleanup');
      this.forceCleanupSprites(groundTrailSprites, maxSpritesPerType);
    }
    if (waterRippleSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many water ripple sprites, forcing cleanup');
      this.forceCleanupSprites(waterRippleSprites, maxSpritesPerType);
    }
    if (windLinesSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many wind line sprites, forcing cleanup');
      this.forceCleanupSprites(windLinesSprites, maxSpritesPerType);
    }
    if (backgroundCloudSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many background cloud sprites, forcing cleanup');
      this.forceCleanupSprites(backgroundCloudSprites, maxSpritesPerType);
    }
    if (foregroundCloudSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many foreground cloud sprites, forcing cleanup');
      this.forceCleanupSprites(foregroundCloudSprites, maxSpritesPerType);
    }
    if (dustCloudSprites.length > maxSpritesPerType) {
      if (DEBUG_MODE) console.warn('⚠️ Too many dust cloud sprites, forcing cleanup');
      this.forceCleanupSprites(dustCloudSprites, maxSpritesPerType);
    }
  };
  
  // Force cleanup when sprite arrays grow too large
  Scene_Map.prototype.forceCleanupSprites = function(spriteArray, maxCount) {
    // Remove oldest sprites first
    const spritesToRemove = spriteArray.splice(0, spriteArray.length - maxCount);
    spritesToRemove.forEach(sprite => {
      if (sprite && sprite.parent) {
        sprite.parent.removeChild(sprite);
        sprite._destroyed = true;
        // FIX: Properly destroy sprites to prevent memory leaks
        if (sprite.destroy) {
          sprite.destroy();
        }
      }
    });
  };
  
  // FIX: Add timer cleanup to prevent memory leaks
  let airshipIntegrityTimer = null;
  
  // Comprehensive cleanup when airship effects are deactivated
  Scene_Map.prototype.cleanupAllAirshipEffects = function() {
    if (DEBUG_MODE) console.log('🧹 Cleaning up all airship effects');
    
    // FIX: Clear the integrity check timer
    if (airshipIntegrityTimer) {
      clearInterval(airshipIntegrityTimer);
      airshipIntegrityTimer = null;
    }
    
    // Instead of immediately destroying particles, let them fade naturally
    // Only clean up if there are too many particles for performance
    const maxSpritesPerType = AIRSHIP_CONFIG.PERFORMANCE.MAX_SPRITES_PER_TYPE;
    
    const allSpriteArrays = [
      { name: 'groundTrail', array: groundTrailSprites },
      { name: 'waterRipple', array: waterRippleSprites },
      { name: 'windLines', array: windLinesSprites },
      { name: 'backgroundCloud', array: backgroundCloudSprites },
      { name: 'foregroundCloud', array: foregroundCloudSprites },
      { name: 'dustCloud', array: dustCloudSprites }
    ];
    
    allSpriteArrays.forEach(({ name, array }) => {
      // Only force cleanup if array is too large for performance
      if (array.length > maxSpritesPerType * 2) {
      let cleanedCount = 0;
        // Remove oldest sprites first, but keep some for natural fade
        const spritesToRemove = array.splice(0, array.length - maxSpritesPerType);
        spritesToRemove.forEach(sprite => {
        if (sprite && sprite.parent) {
          sprite.parent.removeChild(sprite);
          sprite._destroyed = true;
          // FIX: Properly destroy sprites to prevent memory leaks
          if (sprite.destroy) {
            sprite.destroy();
          }
          cleanedCount++;
        }
      });
                              if (DEBUG_MODE) console.log(`🧹 Force cleaned up ${cleanedCount} ${name} sprites (performance)`);
                    } else {
                      if (DEBUG_MODE) console.log(`🧹 Allowing ${array.length} ${name} sprites to fade naturally`);
                    }
    });
    
    // Reset timers but don't clear arrays
    trailCreationTimer = 0;
    rippleCreationTimer = 0;
    windLinesCreationTimer = 0;
    cloudCreationTimer = 0;
    lastAirshipX = 0;
    lastAirshipY = 0;
  };
  
  Scene_Map.prototype.updateGroundTrail = function(density = 1.0) {
    // Performance check: skip if not in airship
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    
    const airship = $gameMap.airship();
    if (!airship || !this._airshipEffectLayers) return;
    
    // Null safety checks
    if (!airship._altitude || typeof airship._altitude !== 'number') return;
    
    const altitude = airship._altitude || 0;
    const airshipX = Math.floor(airship.x);
    const airshipY = Math.floor(airship.y);
    
    // Check if airship is over water
    const isOverWater = $gameMap.isWaterTile ? $gameMap.isWaterTile(airshipX, airshipY) : 
                       ($gameMap.isBoatPassable(airshipX, airshipY) || $gameMap.isShipPassable(airshipX, airshipY));
    
    // Only create trail at low altitude AND when moving AND over water
    // But allow existing particles to continue their natural fade cycle
    if (altitude <= AIRSHIP_CONFIG.EFFECTS.TRAIL_LOW_ALTITUDE && 
        (Input.isPressed('shift') || Input.isPressed('up')) && 
        isOverWater) {
      
      trailCreationTimer++;
      
      // Create trail with dynamic timing based on altitude - more frequent at lower altitudes
      const altitudeBasedTimer = Math.max(1, Math.floor(8 - (altitude / 15))); // 1-8 frames based on altitude
      if (trailCreationTimer >= altitudeBasedTimer + Math.floor(Math.random() * 3)) {
        trailCreationTimer = 0;
        
        // Get airship position in screen coordinates with proper offset calculation
        const x = airship.screenX();
        const y = airship.screenY() + 44; // Offset to match shadow position + 20px down
        
        // Validate coordinates to prevent visual glitches
        if (isNaN(x) || isNaN(y) || x < -100 || x > Graphics.width + 100 || y < -100 || y > Graphics.height + 100) {
          console.warn('⚠️ Invalid airship coordinates detected, skipping trail creation');
          return;
        }
        
        // Determine airship movement direction
        let airshipDirection = 0;
        if (lastAirshipX !== 0 && lastAirshipY !== 0) {
          const deltaX = x - lastAirshipX;
          const deltaY = y - lastAirshipY;
          
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            airshipDirection = deltaX > 0 ? 6 : 4; // Right or Left
          } else {
            airshipDirection = deltaY > 0 ? 2 : 8; // Down or Up
          }
        }
        
        // Add more randomness to make it look more chaotic
        const randomX = x + (Math.random() - 0.5) * 12;
        const randomY = y + (Math.random() - 0.5) * 6;
        
        // Create more trail points at lower altitudes for more dramatic effect
        const baseTrailCount = altitude <= 40 ? 8 : (altitude <= 80 ? 5 : (altitude <= 120 ? 3 : 1)); // Optimized scaling
        const trailCount = Math.floor(baseTrailCount * density);
        
        // Enhanced altitude-based particle scaling
        const altitudeIntensity = Math.max(0.1, 1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.TRAIL_LOW_ALTITUDE)); // More intense at lower altitudes
        const particleSizeMultiplier = 1.0 + (altitudeIntensity * 1.0); // Particles 1-2x larger at low altitude
        const particleSpeedMultiplier = 1.0 + (altitudeIntensity * 1.0); // Particles move 1-2x faster at low altitude
        
        for (let i = 0; i < trailCount; i++) {
          try {
            const trailSprite = new GroundTrailSprite(randomX, randomY, airshipDirection);
            
            // Apply altitude-based scaling to particle properties
            if (trailSprite) {
              // Scale particle size based on altitude
              trailSprite.scale.set(
                trailSprite.scale.x * particleSizeMultiplier,
                trailSprite.scale.y * particleSizeMultiplier
              );
              
              // Adjust particle speed based on altitude
              trailSprite.driftSpeed *= particleSpeedMultiplier;
              trailSprite.horizontalDrift *= particleSpeedMultiplier;
              
              // Adjust opacity based on altitude (more opaque at lower altitudes)
              trailSprite.opacity = Math.min(255, trailSprite.opacity * (1.0 + altitudeIntensity * 0.5));
              
              // Adjust fade time based on altitude (longer fade at lower altitudes)
              trailSprite.fadeTimer = Math.floor(trailSprite.fadeTimer * (1.0 + altitudeIntensity * 0.3));
            }
            
            if (this._airshipEffectLayers.background && trailSprite) {
              this._airshipEffectLayers.background.addChild(trailSprite);
              groundTrailSprites.push(trailSprite);
            }
          } catch (error) {
            console.error('❌ Error creating trail sprite:', error);
          }
        }
        
        // Update last position
        lastAirshipX = x;
        lastAirshipY = y;
        
        // Debug: log trail creation
        console.log(`🌪️ Trail created at altitude ${altitude}, position (${randomX.toFixed(1)}, ${randomY.toFixed(1)})`);
        
        // Limit number of trail points (reduced from 25 to 15)
        if (groundTrailSprites.length > 15) {
          const oldTrail = groundTrailSprites.shift();
          if (oldTrail && oldTrail.parent) {
            oldTrail.parent.removeChild(oldTrail);
          }
        }
      }
    } else {
      // Reset timer when not creating trails
      trailCreationTimer = 0;
      // Reset position tracking when not moving
      lastAirshipX = 0;
      lastAirshipY = 0;
    }
    
    // Create water ripples at very low altitude over water
    if (altitude <= AIRSHIP_CONFIG.EFFECTS.RIPPLE_LOW_ALTITUDE && isOverWater && (Input.isPressed('shift') || Input.isPressed('up'))) {
      rippleCreationTimer++;
      
      // Create ripples with dynamic timing based on altitude - more frequent at lower altitudes
      const rippleAltitudeBasedTimer = Math.max(2, Math.floor(10 - (altitude / 10))); // 2-10 frames based on altitude
      if (rippleCreationTimer >= rippleAltitudeBasedTimer + Math.floor(Math.random() * 4)) {
        rippleCreationTimer = 0;
        
        const rippleX = airship.screenX(); // No random X variation
        const rippleY = airship.screenY() + 80; // Position ripples on water surface
        
        try {
          const waterRipple = new WaterRippleSprite(rippleX, rippleY);
          
          // Enhanced altitude-based water ripple scaling
          if (waterRipple) {
            const rippleAltitudeIntensity = Math.max(0.1, 1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.RIPPLE_LOW_ALTITUDE)); // More intense at very low altitudes
            const rippleSizeMultiplier = 1.0 + (rippleAltitudeIntensity * 1.5); // Ripples 1-2.5x larger at low altitude
            const rippleSpeedMultiplier = 1.0 + (rippleAltitudeIntensity * 1.2); // Ripples expand 1-2.2x faster at low altitude
            
            // Scale ripple size based on altitude
            waterRipple.scale.set(
              waterRipple.scale.x * rippleSizeMultiplier,
              waterRipple.scale.y * rippleSizeMultiplier
            );
            
            // Adjust ripple expansion speed based on altitude
            waterRipple.expansionRate *= rippleSpeedMultiplier;
            
            // Adjust ripple opacity based on altitude (more opaque at lower altitudes)
            waterRipple.opacity = Math.min(255, waterRipple.opacity * (1.0 + rippleAltitudeIntensity * 0.6));
            
            // Adjust ripple fade time based on altitude (longer fade at lower altitudes)
            waterRipple.fadeTimer = Math.floor(waterRipple.fadeTimer * (1.0 + rippleAltitudeIntensity * 0.4));
          }
          
          if (this._airshipEffectLayers.background && waterRipple) {
            this._airshipEffectLayers.background.addChild(waterRipple);
            waterRippleSprites.push(waterRipple);
          }
        } catch (error) {
          console.error('❌ Error creating water ripple sprite:', error);
        }
        
        // Limit number of ripple sprites - optimized limit
        if (waterRippleSprites.length > 20) {
          const oldRipple = waterRippleSprites.shift();
          if (oldRipple && oldRipple.parent) {
            oldRipple.parent.removeChild(oldRipple);
          }
        }
      }
    } else {
      // Reset ripple timer when not creating ripples
      rippleCreationTimer = 0;
    }
    
    // Clear ripples when over land
    if (!isOverWater && waterRippleSprites.length > 0) {
      waterRippleSprites.forEach(sprite => {
        if (sprite && sprite.parent) {
          sprite.parent.removeChild(sprite);
        }
      });
      waterRippleSprites = [];
    }
    
    // Clean up destroyed sprites
    groundTrailSprites = groundTrailSprites.filter(sprite => sprite.parent);
    waterRippleSprites = waterRippleSprites.filter(sprite => sprite.parent);
    
    // Create wind lines at screen edges when moving - only above certain altitude
    if ((Input.isPressed('shift') || Input.isPressed('up')) && airship._altitude >= AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) {
      windLinesCreationTimer++;
      
      // Create wind lines with dynamic timing based on altitude - more frequent at higher altitudes
      const windAltitudeBasedTimer = Math.max(1, Math.floor(4 - ((altitude - AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) / 20))); // 1-4 frames based on altitude
      if (windLinesCreationTimer >= windAltitudeBasedTimer + Math.floor(Math.random() * 2)) {
        windLinesCreationTimer = 0;
        
        // Create wind lines at screen edges
        const screenWidth = Graphics.width;
        const screenHeight = Graphics.height;
        
        // Create more wind lines per frame (2-4 instead of 3-5)
        const lineCount = 2 + Math.floor(Math.random() * 3);
        
        for (let i = 0; i < lineCount; i++) {
          // Random position at screen edges
          let x, y, direction;
          
          if (Math.random() < 0.25) {
            // Top edge
            x = Math.random() * screenWidth;
            y = 50; // Closer to center
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.5) {
            // Right edge
            x = screenWidth - 50; // Closer to center
            y = Math.random() * screenHeight;
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.75) {
            // Bottom edge
            x = Math.random() * screenWidth;
            y = screenHeight - 50; // Closer to center
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else {
            // Left edge
            x = 50; // Closer to center
            y = Math.random() * screenHeight;
            // Calculate radial direction from center to this point
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          }
          
          const windLine = new WindLinesSprite(x, y, direction);
          
          // Enhanced altitude-based wind line scaling
          if (windLine) {
            const windAltitudeIntensity = Math.max(0.1, (altitude - AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) / 80); // More intense at higher altitudes
            const windSizeMultiplier = 1.0 + (windAltitudeIntensity * 0.8); // Wind lines 1-1.8x larger at high altitude
            const windSpeedMultiplier = 1.0 + (windAltitudeIntensity * 2.0); // Wind lines move 1-3x faster at high altitude
            
            // Scale wind line size based on altitude
            windLine.scale.set(
              windLine.scale.x * windSizeMultiplier,
              windLine.scale.y * windSizeMultiplier
            );
            
            // Adjust wind line opacity based on altitude (more opaque at higher altitudes)
            windLine.opacity = Math.min(255, windLine.opacity * (1.0 + windAltitudeIntensity * 0.7));
            
            // Adjust wind line fade time based on altitude (longer fade at higher altitudes)
            windLine.fadeTimer = Math.floor(windLine.fadeTimer * (1.0 + windAltitudeIntensity * 0.3));
          }
          
          this._airshipEffectLayers.midground.addChild(windLine);
          windLinesSprites.push(windLine);
        }
        
        // Add inner wind lines (closer to center, more transparent)
        const innerLineCount = Math.random() < 0.5 ? 1 : 0; // 50% chance for 1 inner line, otherwise 0
        
        for (let i = 0; i < innerLineCount; i++) {
          // Random position closer to center
          let x, y, direction;
          
          if (Math.random() < 0.25) {
            // Top area
            x = Math.random() * screenWidth;
            y = 100; // Even closer to center
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.5) {
            // Right area
            x = screenWidth - 100; // Even closer to center
            y = Math.random() * screenHeight;
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else if (Math.random() < 0.75) {
            // Bottom area
            x = Math.random() * screenWidth;
            y = screenHeight - 100; // Even closer to center
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          } else {
            // Left area
            x = 100; // Even closer to center
            y = Math.random() * screenHeight;
            direction = Math.atan2(y - screenHeight/2, x - screenWidth/2) * (180 / Math.PI);
          }
          
          const innerWindLine = new InnerWindLinesSprite(x, y, direction);
          
          // Enhanced altitude-based inner wind line scaling
          if (innerWindLine) {
            const innerWindAltitudeIntensity = Math.max(0.1, (altitude - AIRSHIP_CONFIG.EFFECTS.WIND_LINES_ALTITUDE) / 60); // More intense at higher altitudes
            const innerWindSizeMultiplier = 1.0 + (innerWindAltitudeIntensity * 0.6); // Inner wind lines 1-1.6x larger at high altitude
            const innerWindSpeedMultiplier = 1.0 + (innerWindAltitudeIntensity * 1.8); // Inner wind lines move 1-2.8x faster at high altitude
            
            // Scale inner wind line size based on altitude
            innerWindLine.scale.set(
              innerWindLine.scale.x * innerWindSizeMultiplier,
              innerWindLine.scale.y * innerWindSizeMultiplier
            );
            
            // Adjust inner wind line opacity based on altitude (more opaque at higher altitudes)
            innerWindLine.opacity = Math.min(255, innerWindLine.opacity * (1.0 + innerWindAltitudeIntensity * 0.5));
            
            // Adjust inner wind line fade time based on altitude (longer fade at higher altitudes)
            innerWindLine.fadeTimer = Math.floor(innerWindLine.fadeTimer * (1.0 + innerWindAltitudeIntensity * 0.2));
          }
          
          this._airshipEffectLayers.midground.addChild(innerWindLine);
          windLinesSprites.push(innerWindLine);
        }
        
        // Limit number of wind line sprites (reduced from 25 to 15)
        if (windLinesSprites.length > 15) {
          const oldWindLine = windLinesSprites.shift();
          if (oldWindLine && oldWindLine.parent) {
            oldWindLine.parent.removeChild(oldWindLine);
          }
        }
      }
    } else {
      // Reset wind lines timer when not moving
      windLinesCreationTimer = 0;
    }
    
    // Clean up wind lines sprites
    windLinesSprites = windLinesSprites.filter(sprite => sprite.parent);
  };
  
  Scene_Map.prototype.updateCloudFlying = function(density = 1.0) {
    // Performance check: skip if not in airship
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    
    const airship = $gameMap.airship();
    if (!airship || !this._airshipEffectLayers) return;
    
    // Null safety checks
    if (!airship._altitude || typeof airship._altitude !== 'number') return;
    
    const altitude = airship._altitude || 0;
    const isMoving = Input.isPressed('shift') || Input.isPressed('up');
    
    // Create cloud flying effect at high altitude
    if (altitude >= AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE && isMoving) {
      cloudCreationTimer++;
      
      // Create clouds with dynamic timing based on altitude - more frequent at higher altitudes
      const cloudAltitudeBasedTimer = Math.max(1, Math.floor(5 - ((altitude - AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE) / 30))); // 1-5 frames based on altitude
      if (cloudCreationTimer >= cloudAltitudeBasedTimer + Math.floor(Math.random() * 3)) {
        cloudCreationTimer = 0;
        
        const screenWidth = Graphics.width;
        const screenHeight = Graphics.height;
        const centerX = screenWidth / 2;
        const centerY = screenHeight / 2;
        
        // Enhanced cloud density based on altitude
        const altitudeFactor = Math.min((altitude - AIRSHIP_CONFIG.EFFECTS.CLOUD_FLYING_ALTITUDE) / 50, 2.0); // Reduced from 2.5 to 2.0
        const baseCloudCount = Math.floor((1 + Math.floor(Math.random() * 2)) * altitudeFactor * EffectSystems.cloudFlying.config.densityMultiplier); // Reduced from 1-4 to 1-3
        const cloudCount = Math.floor(baseCloudCount * density);
        
        for (let i = 0; i < cloudCount; i++) {
          // Spawn clouds from horizon with radial flow pattern
          const screenWidth = Graphics.width;
          const screenHeight = Graphics.height;
          const horizonY = screenHeight * 0.5// Horizon at 70% down the screen
          
          // Spawn clouds along the horizon line
          const spawnX = Math.random() * screenWidth; // Random X along horizon
          const spawnY = horizonY + (Math.random() - 0.5) * 40; // Slight variation in Y
          
          // Calculate destination based on spawn position
          // Left side clouds flow down and left, right side flow down and right
          const centerX = screenWidth / 2;
          const destX = centerX + (spawnX - centerX) * 3 // Outward flow in same direction
          const destY = screenHeight + 50; // Flow down to bottom
          
          // Calculate direction from spawn to destination
          const direction = Math.atan2(destY - spawnY, destX - spawnX);
          
          // Random cloud size and opacity (increased opacity and scale)
          const size = 40 + Math.random() * 60; // 40-100px clouds (increased from 30-70)
          const opacity = 150 + Math.random() * 105; // 150-255 opacity (increased from 100-200)
          
          // Enhanced cloud layering system
          const screenHalfway = Graphics.height / 2;
          const isBackgroundCloud = spawnY < screenHalfway || Math.random() < 0.3; // 30% chance for background
          
          if (isBackgroundCloud) {
            // Background clouds - identical to foreground except slower and more transparent
            const bgSpeedMultiplier = 0.3; // Slower than foreground
            const bgOpacity = opacity * 0.7; // Increased from 0.5 to 0.7 for better visibility
            
            const bgCloud = new CloudSprite(spawnX, spawnY, size, bgOpacity, direction, bgSpeedMultiplier);
            this._airshipEffectLayers.background.addChild(bgCloud);
            backgroundCloudSprites.push(bgCloud);
          } else {
            // Foreground clouds - faster and more opaque
            const fgSpeedMultiplier = spawnY < screenHalfway ? 0.35 : 1.3;
            const fgOpacity = opacity * 1.6; // Increased from 1.4 to 1.6 for more opacity
            
            const fgCloud = new CloudSprite(spawnX, spawnY, size, fgOpacity, direction, fgSpeedMultiplier);
            this._airshipEffectLayers.foreground.addChild(fgCloud);
            foregroundCloudSprites.push(fgCloud);
          }
        }
        
        // Limit number of cloud sprites for each layer (reduced from 20 to 15)
        if (backgroundCloudSprites.length > 15) {
          const oldCloud = backgroundCloudSprites.shift();
          if (oldCloud && oldCloud.parent) {
            oldCloud.parent.removeChild(oldCloud);
          }
        }
        
        if (foregroundCloudSprites.length > 15) {
          const oldCloud = foregroundCloudSprites.shift();
          if (oldCloud && oldCloud.parent) {
            oldCloud.parent.removeChild(oldCloud);
          }
        }
      }
    } else {
      // Reset timer when not creating clouds
      cloudCreationTimer = 0;
    }
    
    // Clean up destroyed cloud sprites for both layers
    backgroundCloudSprites = backgroundCloudSprites.filter(sprite => sprite.parent);
    foregroundCloudSprites = foregroundCloudSprites.filter(sprite => sprite.parent);
  };
  
  Scene_Map.prototype.updateLowAltitudeEffects = function(density = 1.0) {
    // Performance check: skip if not in airship
    if (!$gamePlayer || !$gamePlayer.isInAirship()) return;
    
    const airship = $gameMap.airship();
    if (!airship || !this._airshipEffectLayers) return;
    
    // Null safety checks
    if (!airship._altitude || typeof airship._altitude !== 'number') return;
    
    const altitude = airship._altitude || 0;
    const isMoving = Input.isPressed('shift') || Input.isPressed('up');
    const airshipX = Math.floor(airship.x);
    const airshipY = Math.floor(airship.y);
    
    // Check if airship is over water
    const isOverWater = $gameMap.isWaterTile ? $gameMap.isWaterTile(airshipX, airshipY) : 
                       ($gameMap.isBoatPassable(airshipX, airshipY) || $gameMap.isShipPassable(airshipX, airshipY));
    
    // Create dust clouds at medium-low altitude (only on land)
    if (altitude <= AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD && isMoving && !isOverWater) {
      // Dynamic dust creation probability based on altitude - more likely at lower altitudes
      const dustCreationProbability = Math.min(0.7, 0.2 + (0.5 * (1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD)))); // 20-70% based on altitude
      if (Math.random() < dustCreationProbability) {
        const x = airship.screenX();
        const y = airship.screenY() + 44; // Same offset as trail
        
        // Determine airship movement direction (same logic as trail)
        let airshipDirection = 0;
        if (lastAirshipX !== 0 && lastAirshipY !== 0) {
          const deltaX = x - lastAirshipX;
          const deltaY = y - lastAirshipY;
          
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            airshipDirection = deltaX > 0 ? 6 : 4; // Right or Left
          } else {
            airshipDirection = deltaY > 0 ? 2 : 8; // Down or Up
          }
        }
        
        // Add more randomness to make it look more chaotic (same as trail)
        const randomX = x + (Math.random() - 0.5) * 12;
        const randomY = y + (Math.random() - 0.5) * 6;
        
        // Create more dust particles at lower altitudes (same logic as trail)
        const baseDustCount = altitude <= 40 ? 8 : (altitude <= 80 ? 5 : (altitude <= 120 ? 3 : 1)); // Optimized scaling
        const dustCount = Math.floor(baseDustCount * density);
        
        // Enhanced altitude-based dust particle scaling
        const altitudeIntensity = Math.max(0.1, 1.0 - (altitude / AIRSHIP_CONFIG.EFFECTS.DUST_CLOUD_THRESHOLD)); // More intense at lower altitudes for dust
        const dustSizeMultiplier = 1.0 + (altitudeIntensity * 1.2); // Dust particles 1-2.2x larger at low altitude
        const dustSpeedMultiplier = 1.0 + (altitudeIntensity * 1.0); // Dust particles move 1-2x faster at low altitude
        
        for (let i = 0; i < dustCount; i++) {
          try {
                      const dustCloud = new DustCloudSprite(randomX, randomY, airshipDirection);
            
            // Apply altitude-based scaling to dust particle properties
            if (dustCloud) {
              // Scale dust particle size based on altitude
              dustCloud.scale.set(
                dustCloud.scale.x * dustSizeMultiplier,
                dustCloud.scale.y * dustSizeMultiplier
              );
              
              // Adjust dust particle speed based on altitude
              dustCloud.driftSpeed *= dustSpeedMultiplier;
              dustCloud.horizontalDrift *= dustSpeedMultiplier;
              
              // Adjust dust opacity based on altitude (more opaque at lower altitudes)
              dustCloud.opacity = Math.min(255, dustCloud.opacity * (1.0 + altitudeIntensity * 0.8));
              
              // Adjust dust fade time based on altitude (longer fade at lower altitudes)
              dustCloud.fadeTimer = Math.floor(dustCloud.fadeTimer * (1.0 + altitudeIntensity * 0.5));
            }
            
            this._airshipEffectLayers.midground.addChild(dustCloud);
            dustCloudSprites.push(dustCloud);
          } catch (error) {
            console.error('❌ Error creating dust cloud sprite:', error);
          }
        }
        
        // Update last position (same as trail)
        lastAirshipX = x;
        lastAirshipY = y;
      }
    }
    
    // Clean up destroyed sprites
    dustCloudSprites = dustCloudSprites.filter(sprite => sprite.parent);
  };
  

  
  // Extend the existing getOffVehicle function to clear trail
  const _getOffVehicle_original = Game_Player.prototype.getOffVehicle;
  Game_Player.prototype.getOffVehicle = function() {
    const result = _getOffVehicle_original.call(this);
    
    // Use the comprehensive cleanup system
    if (SceneManager._scene && SceneManager._scene.cleanupAllAirshipEffects) {
      SceneManager._scene.cleanupAllAirshipEffects();
    }
    
    return result;
  };
  
  // Add scene transition cleanup
  const _Scene_Map_terminate = Scene_Map.prototype.terminate;
  Scene_Map.prototype.terminate = function() {
    // Clean up airship effects before scene termination
    if (this.cleanupAllAirshipEffects) {
      this.cleanupAllAirshipEffects();
    }
    
    // FIX: Clear the integrity check timer on scene termination
    if (airshipIntegrityTimer) {
      clearInterval(airshipIntegrityTimer);
      airshipIntegrityTimer = null;
    }
    
    _Scene_Map_terminate.call(this);
  };
  
  // Add error recovery for unexpected crashes
  const _Scene_Map_start = Scene_Map.prototype.start;
  Scene_Map.prototype.start = function() {
    _Scene_Map_start.call(this);
    
    // Reset airship state on scene start
    isAirshipActive = false;
    frameCounter = 0;
    
    // FIX: Clear any existing timer before creating new one
    if (airshipIntegrityTimer) {
      clearInterval(airshipIntegrityTimer);
    }
    
    // Emergency cleanup if arrays are corrupted
    if (groundTrailSprites && groundTrailSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: groundTrailSprites array corrupted');
      groundTrailSprites = [];
    }
    if (waterRippleSprites && waterRippleSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: waterRippleSprites array corrupted');
      waterRippleSprites = [];
    }
    if (windLinesSprites && windLinesSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: windLinesSprites array corrupted');
      windLinesSprites = [];
    }
    if (backgroundCloudSprites && backgroundCloudSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: backgroundCloudSprites array corrupted');
      backgroundCloudSprites = [];
    }
    if (foregroundCloudSprites && foregroundCloudSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: foregroundCloudSprites array corrupted');
      foregroundCloudSprites = [];
    }
    if (dustCloudSprites && dustCloudSprites.length > 100) {
      console.warn('🚨 Emergency cleanup: dustCloudSprites array corrupted');
      dustCloudSprites = [];
    }
  };

  // Fix tile loading at map edges by increasing extend tiles
  UltraMode7.LOOP_MAPS_EXTEND_TILES = 8; // Increased from 3 to 8 for better edge coverage
  
  // Global error recovery and debugging system
  window.MAUI_FF6Airship_ErrorRecovery = {
    // Debug information
    debugInfo: {
      totalSprites: 0,
      frameRate: 0,
      memoryUsage: 0,
      lastUpdate: Date.now(),
      bitmapPoolStats: null,
      memoryLeaks: []
    },
    
    // Update debug information
    updateDebugInfo: function() {
      const arrays = [groundTrailSprites, waterRippleSprites, windLinesSprites, 
                     backgroundCloudSprites, foregroundCloudSprites, dustCloudSprites];
      
      this.debugInfo.totalSprites = arrays.reduce((total, array) => total + array.length, 0);
      this.debugInfo.frameRate = Graphics.frameRate;
      this.debugInfo.lastUpdate = Date.now();
      
      // Check for bitmap pool memory leaks
      if (BitmapPool && BitmapPool.checkForLeaks) {
        this.debugInfo.memoryLeaks = BitmapPool.checkForLeaks();
        this.debugInfo.bitmapPoolStats = BitmapPool.getStats();
      }
      
      // Log debug info every 5 seconds
      if (this.debugInfo.lastUpdate % 5000 < 16) {
        console.log('🔍 Airship Debug Info:', {
          totalSprites: this.debugInfo.totalSprites,
          frameRate: this.debugInfo.frameRate,
          isAirshipActive: isAirshipActive,
          bitmapPoolStats: this.debugInfo.bitmapPoolStats,
          memoryLeaks: this.debugInfo.memoryLeaks ? this.debugInfo.memoryLeaks.length : 0
        });
        
        // Warn about memory leaks
        if (this.debugInfo.memoryLeaks && this.debugInfo.memoryLeaks.length > 0) {
          console.warn('🚨 Memory leaks detected in BitmapPool:', this.debugInfo.memoryLeaks);
        }
      }
    },
    // Force cleanup all airship effects
    forceCleanup: function() {
      console.log('🚨 Emergency airship effects cleanup triggered');
      
      // FIX: Clear the integrity check timer
      if (airshipIntegrityTimer) {
        clearInterval(airshipIntegrityTimer);
        airshipIntegrityTimer = null;
      }
      
      // Clear all sprite arrays
      const arrays = [groundTrailSprites, waterRippleSprites, windLinesSprites, 
                     backgroundCloudSprites, foregroundCloudSprites, dustCloudSprites];
      
      arrays.forEach(array => {
        if (array && Array.isArray(array)) {
          array.forEach(sprite => {
      if (sprite && sprite.parent) {
              try {
        sprite.parent.removeChild(sprite);
                sprite._destroyed = true;
                // FIX: Properly destroy sprites to prevent memory leaks
                if (sprite.destroy) {
                  sprite.destroy();
                }
              } catch (error) {
                console.error('❌ Error during emergency sprite cleanup:', error);
              }
            }
          });
          array.length = 0;
        }
      });
      
      // FIX: Clear bitmap pool to prevent memory leaks
      if (BitmapPool && BitmapPool.clear) {
        BitmapPool.clear();
      }
      
      // Reset all timers and state
      trailCreationTimer = 0;
      rippleCreationTimer = 0;
      windLinesCreationTimer = 0;
      cloudCreationTimer = 0;
      lastAirshipX = 0;
      lastAirshipY = 0;
      isAirshipActive = false;
      frameCounter = 0;
      
      console.log('✅ Emergency cleanup completed');
    },
    
    // Check for corrupted state
    checkIntegrity: function() {
      const arrays = [groundTrailSprites, waterRippleSprites, windLinesSprites, 
                     backgroundCloudSprites, foregroundCloudSprites, dustCloudSprites];
      
      let totalSprites = 0;
      arrays.forEach(array => {
        if (array && Array.isArray(array)) {
          totalSprites += array.length;
        }
      });
      
      // Check for memory leaks in bitmap pool
      let hasMemoryLeaks = false;
      if (BitmapPool && BitmapPool.checkForLeaks) {
        const leaks = BitmapPool.checkForLeaks();
        if (leaks && leaks.length > 0) {
          hasMemoryLeaks = true;
          console.warn('🚨 BitmapPool memory leaks detected during integrity check');
        }
      }
      
      if (totalSprites > 800 || hasMemoryLeaks) { // Reduced from 1000 to 800 for better performance
        console.warn('🚨 Too many sprites or memory leaks detected, triggering emergency cleanup');
        this.forceCleanup();
        return false;
      }
      
      return true;
    }
  };
  
  // FIX: Use timer variable for proper cleanup
  airshipIntegrityTimer = setInterval(() => {
    if (window.MAUI_FF6Airship_ErrorRecovery) {
      window.MAUI_FF6Airship_ErrorRecovery.checkIntegrity();
      window.MAUI_FF6Airship_ErrorRecovery.updateDebugInfo();
    }
  }, 60000); // Check every 60 seconds (reduced frequency)
  
  console.log('✅ MAUI_FF6Airship plugin loaded with error recovery system');

})();

